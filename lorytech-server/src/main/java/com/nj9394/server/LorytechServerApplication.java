package com.nj9394.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 项目的启动类
 *
 * 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${lorytech.info.base-package}
@SpringBootApplication(scanBasePackages = {"${lorytech.info.base-package}.server", "${lorytech.info.base-package}.module"})
public class LorytechServerApplication {

    public static void main(String[] args) {
        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章

        SpringApplication.run(LorytechServerApplication.class, args);
//        new SpringApplicationBuilder(LorytechServerApplication.class)
//                .applicationStartup(new BufferingApplicationStartup(20480))
//                .run(args);

        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
        // 如果你碰到启动的问题，请认真阅读 https://doc.nj9394.com/quick-start/ 文章
    }

}
