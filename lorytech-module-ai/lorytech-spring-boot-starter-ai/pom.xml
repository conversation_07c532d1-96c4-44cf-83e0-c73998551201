<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nj9394.boot</groupId>
        <artifactId>lorytech-module-ai</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lorytech-spring-boot-starter-ai</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>AI 大模型拓展，接入国内外大模型</description>
    <properties>
        <spring-ai.version>1.0.0-M1</spring-ai.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-zhipuai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-ollama-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-stability-ai-spring-boot-starter</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-common</artifactId>
        </dependency>

        <!-- TODO 芋艿：等 spring-ai 官方发布后，需要把 groupId 改下 -->
        <dependency>
            <groupId>group.springframework.ai</groupId>
            <artifactId>spring-ai-qianfan-spring-boot-starter</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- 阿里云 通义千问 -->
        <!-- TODO 芋艿：等 spring cloud alibaba ai 发布最新的时候，可以替换掉这个依赖，并且删除我们直接 cv 的代码 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.14.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>