<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nj9394.boot</groupId>
        <artifactId>lorytech-module-erp</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lorytech-module-erp-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        erp 包下，企业资源管理（Enterprise Resource Planning）。
        例如说：采购、销售、库存、财务、物资等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-module-erp-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
<!--        <dependency>-->
<!--            <groupId>com.nj9394.boot</groupId>-->
<!--            <artifactId>lorytech-module-bpm-api</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->


        <!-- Web 相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-test</artifactId>
        </dependency>

    </dependencies>

</project>
