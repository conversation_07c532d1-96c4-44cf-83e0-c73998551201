package com.nj9394.module.erp.service.stock;

import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyItemDO;
import com.nj9394.module.erp.dal.mysql.stockoutapply.StockOutApplyItemMapper;
import com.nj9394.module.erp.dal.mysql.stockoutapply.StockOutApplyMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.nj9394.module.erp.controller.admin.stockposition.vo.*;
import com.nj9394.module.erp.dal.dataobject.stockposition.ErpStockPositionDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.erp.dal.mysql.stock.ErpStockPositionMapper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.erp.enums.ErrorCodeConstants.*;

/**
 * ERP 产品库存仓位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpStockPositionServiceImpl implements ErpStockPositionService {

    @Resource
    private ErpStockPositionMapper erpStockPositionMapper;
    @Resource
    private StockOutApplyItemMapper stockOutApplyItemMapper;

    @Override
    public Long createStockPosition(ErpStockPositionSaveReqVO createReqVO) {
        // 插入
        ErpStockPositionDO stockPosition = BeanUtils.toBean(createReqVO, ErpStockPositionDO.class);
        erpStockPositionMapper.insert(stockPosition);
        // 返回
        return stockPosition.getId();
    }

    @Override
    public void updateStockPosition(ErpStockPositionSaveReqVO updateReqVO) {
        // 校验存在
        validateStockPositionExists(updateReqVO.getId());
        // 更新
        ErpStockPositionDO updateObj = BeanUtils.toBean(updateReqVO, ErpStockPositionDO.class);
        erpStockPositionMapper.updateById(updateObj);
    }

    @Override
    public void deleteStockPosition(Long id) {
        // 校验存在
        validateStockPositionExists(id);
        // 删除
        erpStockPositionMapper.deleteById(id);
    }

    private void validateStockPositionExists(Long id) {
        if (erpStockPositionMapper.selectById(id) == null) {
            throw exception(WAREHOUSE_POSITION_NOT_EXISTS);
        }
    }

    @Override
    public ErpStockPositionDO getStockPosition(Long id) {
        return erpStockPositionMapper.selectById(id);
    }

    @Override
    public PageResult<ErpStockPositionDO> getStockPositionPage(ErpStockPositionPageReqVO pageReqVO) {
        return erpStockPositionMapper.selectPage(pageReqVO);
    }

    @Override
    public BigDecimal getStockPositionAmount(Long positionId, Long warehouseId, Long productId) {
        return erpStockPositionMapper.selectSumByPosition(positionId,warehouseId,productId);
    }

    @Override
    public List<ErpStockPositionRespVO> getStockPositionList(List<Long> ids) {
        //获取所有领料单中的产品ID
        List<StockOutApplyItemDO> list=stockOutApplyItemMapper.selectList(StockOutApplyItemDO::getApplicationId,ids);
        List<Long> idList=new ArrayList<>();;
        list.forEach(apply->{
            idList.add(apply.getProductId());
        });
        return erpStockPositionMapper.selectList(idList);
    }

}