package com.nj9394.module.erp.service.product;

import com.nj9394.module.erp.controller.admin.product.vo.category.ErpProductCategoryListReqVO;
import com.nj9394.module.erp.controller.admin.product.vo.category.ErpProductCategorySaveReqVO;
import com.nj9394.module.erp.dal.dataobject.product.ErpProductCategoryDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.nj9394.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * ERP 物资分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpProductCategoryService {

    /**
     * 创建物资分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductCategory(@Valid ErpProductCategorySaveReqVO createReqVO);

    /**
     * 更新物资分类
     *
     * @param updateReqVO 更新信息
     */
    void updateProductCategory(@Valid ErpProductCategorySaveReqVO updateReqVO);

    /**
     * 删除物资分类
     *
     * @param id 编号
     */
    void deleteProductCategory(Long id);

    /**
     * 获得物资分类
     *
     * @param id 编号
     * @return 物资分类
     */
    ErpProductCategoryDO getProductCategory(Long id);

    /**
     * 获得物资分类列表
     *
     * @param listReqVO 查询条件
     * @return 物资分类列表
     */
    List<ErpProductCategoryDO> getProductCategoryList(ErpProductCategoryListReqVO listReqVO);

    /**
     * 获得物资分类列表
     *
     * @param ids 编号数组
     * @return 物资分类列表
     */
    List<ErpProductCategoryDO> getProductCategoryList(Collection<Long> ids);

    /**
     * 获得物资分类 Map
     *
     * @param ids 编号数组
     * @return 物资分类 Map
     */
    default Map<Long, ErpProductCategoryDO> getProductCategoryMap(Collection<Long> ids) {
        return convertMap(getProductCategoryList(ids), ErpProductCategoryDO::getId);
    }

}