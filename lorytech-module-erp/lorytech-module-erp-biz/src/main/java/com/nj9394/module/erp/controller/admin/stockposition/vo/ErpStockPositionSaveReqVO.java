package com.nj9394.module.erp.controller.admin.stockposition.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - ERP 产品库存仓位新增/修改 Request VO")
@Data
public class ErpStockPositionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12225")
    private Long id;

    @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1118")
    @NotNull(message = "产品编号不能为空")
    private Long productId;

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17379")
    @NotNull(message = "仓库编号不能为空")
    private Long warehouseId;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "16729")
    @NotNull(message = "库存数量不能为空")
    private BigDecimal count;

    @Schema(description = "仓位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7955")
    @NotNull(message = "仓位ID不能为空")
    private Long positionId;

}