package com.nj9394.module.erp.controller.admin.stockoutapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - ERP 领料申请项新增/修改 Request VO")
@Data
public class ErpStockOutApplyItemPageReqVO {
    /**
     * 领料申请项编号
     */
    @Schema(description = "领料申请项编号", example = "你说的对")
    private Long id;
    /**
     * 领料申请编号
     */
    @Schema(description = "领料申请编号", example = "你说的对")
    private Long applicationId;
    /**
     * 产品编号了
     */
    @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private Long productId;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String productName;
    /**
     * 产品数量
     */
    @Schema(description = "产品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private BigDecimal count;
    /**
     * 备注
     */
    @Schema(description = "备注", example = "你说的对")
    private String remark;
}
