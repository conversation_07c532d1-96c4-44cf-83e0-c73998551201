package com.nj9394.module.erp.dal.mysql.stockoutapply;

import java.math.BigDecimal;
import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.erp.dal.dataobject.stock.ErpStockDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyItemDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * ERP 领料申请项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StockOutApplyItemMapper extends BaseMapperX<StockOutApplyItemDO> {

    default PageResult<StockOutApplyItemDO> selectPage(PageParam reqVO, Long applicationId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<StockOutApplyItemDO>()
            .eq(StockOutApplyItemDO::getApplicationId, applicationId)
            .orderByDesc(StockOutApplyItemDO::getId));
    }

    default int deleteByApplicationId(Long applicationId) {
        return delete(StockOutApplyItemDO::getApplicationId, applicationId);
    }


    default BigDecimal selectSumByProductId(Long productId) {
        // SQL sum 查询
        List<Map<String, Object>> result = selectMaps(new QueryWrapper<StockOutApplyItemDO>()
                .select("SUM(count) AS sumCount")
                .eq("product_id", productId));
        // 获得数量
        if (CollUtil.isEmpty(result)) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(MapUtil.getDouble(result.get(0), "sumCount", 0D));
    }

}
