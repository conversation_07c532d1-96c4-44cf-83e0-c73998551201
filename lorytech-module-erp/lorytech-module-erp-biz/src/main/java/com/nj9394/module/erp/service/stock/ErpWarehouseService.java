package com.nj9394.module.erp.service.stock;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.module.erp.controller.admin.stock.vo.warehouse.ErpWarehouseSaveReqVO;
import com.nj9394.module.erp.controller.admin.stock.vo.warehouse.ErpWarehousePageReqVO;
import com.nj9394.module.erp.dal.dataobject.stock.ErpWarehouseDO;
import com.nj9394.module.erp.dal.dataobject.stock.ErpWarehousePositionDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.nj9394.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * ERP 仓库 Service 接口
 *
 * <AUTHOR>
 */
public interface ErpWarehouseService {

    /**
     * 创建仓库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWarehouse(@Valid ErpWarehouseSaveReqVO createReqVO);

    /**
     * 更新ERP 仓库
     *
     * @param updateReqVO 更新信息
     */
    void updateWarehouse(@Valid ErpWarehouseSaveReqVO updateReqVO);

    /**
     * 更新仓库默认状态
     *
     * @param id     编号
     * @param defaultStatus 默认状态
     */
    void updateWarehouseDefaultStatus(Long id, Boolean defaultStatus);

    /**
     * 删除仓库
     *
     * @param id 编号
     */
    void deleteWarehouse(Long id);

    /**
     * 获得仓库
     *
     * @param id 编号
     * @return 仓库
     */
    ErpWarehouseDO getWarehouse(Long id);

    /**
     * 校验仓库列表的有效性
     *
     * @param ids 编号数组
     * @return 仓库列表
     */
    List<ErpWarehouseDO> validWarehouseList(Collection<Long> ids);

    /**
     * 获得指定状态的仓库列表
     *
     * @param status 状态
     * @return 仓库列表
     */
    List<ErpWarehouseDO> getWarehouseListByStatus(Integer status);

    /**
     * 获得仓库列表
     *
     * @param ids 编号数组
     * @return 仓库列表
     */
    List<ErpWarehouseDO> getWarehouseList(Collection<Long> ids);

    /**
     * 获得仓库 Map
     *
     * @param ids 编号数组
     * @return 仓库 Map
     */
    default Map<Long, ErpWarehouseDO> getWarehouseMap(Collection<Long> ids) {
        return convertMap(getWarehouseList(ids), ErpWarehouseDO::getId);
    }

    /**
     * 获得仓库分页
     *
     * @param pageReqVO 分页查询
     * @return 仓库分页
     */
    PageResult<ErpWarehouseDO> getWarehousePage(ErpWarehousePageReqVO pageReqVO);



    // ==================== 子表（仓位） ====================

    /**
     * 获得仓位分页
     *
     * @param pageReqVO 分页查询
     * @param warehouseId 仓库ID
     * @return 仓位分页
     */
    PageResult<ErpWarehousePositionDO> getWarehousePositionPage(PageParam pageReqVO, Long warehouseId);

    /**
     * 创建仓位
     *
     * @param warehousePosition 创建信息
     * @return 编号
     */
    Long createWarehousePosition(@Valid ErpWarehousePositionDO warehousePosition);

    /**
     * 更新仓位
     *
     * @param warehousePosition 更新信息
     */
    void updateWarehousePosition(@Valid ErpWarehousePositionDO warehousePosition);

    /**
     * 删除仓位
     *
     * @param id 编号
     */
    void deleteWarehousePosition(Long id);

    /**
     * 获得仓位
     *
     * @param id 编号
     * @return 仓位
     */
   ErpWarehousePositionDO getWarehousePosition(Long id);

    /**
     * 更新仓为默认状态
     *
     * @param id     编号
     * @param defaultStatus 默认状态
     */
    void updateWarehousePositionDefaultStatus(Long id, Boolean defaultStatus);


    /**
     * 获得指定状态的仓库列表
     *
     * @param status 状态
     * @return 仓位列表
     */
    List<ErpWarehousePositionDO>  getWarehousePositionListByStatus( Long id,Integer status);

    /**
     * 获得仓位列表
     *
     * @param ids 编号数组
     * @return 仓位列表
     */
    List<ErpWarehousePositionDO> getWarehousePositionList(Collection<Long> ids);


    /**
     * 获得仓位 Map
     *
     * @param ids 编号数组
     * @return 仓位 Map
     */
    default Map<Long, ErpWarehousePositionDO> getWarehousePositionMap(Collection<Long> ids) {
        return convertMap(getWarehousePositionList(ids), ErpWarehousePositionDO::getId);
    }

}