package com.nj9394.module.erp.controller.admin.stockposition.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.nj9394.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.nj9394.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - ERP 产品库存仓位分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ErpStockPositionPageReqVO extends PageParam {

    @Schema(description = "产品编号", example = "1118")
    private Long productId;

    @Schema(description = "仓库编号", example = "17379")
    private Long warehouseId;

    @Schema(description = "库存数量", example = "16729")
    private BigDecimal count;

    @Schema(description = "仓位ID", example = "7955")
    private Long positionId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}