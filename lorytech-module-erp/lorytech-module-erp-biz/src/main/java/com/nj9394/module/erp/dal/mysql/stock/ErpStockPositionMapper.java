package com.nj9394.module.erp.dal.mysql.stock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.interfaces.MPJBaseJoin;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nj9394.module.erp.dal.dataobject.product.ErpProductDO;
import com.nj9394.module.erp.dal.dataobject.product.ErpProductUnitDO;
import com.nj9394.module.erp.dal.dataobject.stock.ErpStockDO;
import com.nj9394.module.erp.dal.dataobject.stock.ErpWarehouseDO;
import com.nj9394.module.erp.dal.dataobject.stock.ErpWarehousePositionDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyDO;
import com.nj9394.module.erp.dal.dataobject.stockposition.ErpStockPositionDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.erp.controller.admin.stockposition.vo.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * ERP 产品库存仓位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpStockPositionMapper extends BaseMapperX<ErpStockPositionDO> {

    default PageResult<ErpStockPositionDO> selectPage(ErpStockPositionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ErpStockPositionDO>()
                .eqIfPresent(ErpStockPositionDO::getProductId, reqVO.getProductId())
                .eqIfPresent(ErpStockPositionDO::getWarehouseId, reqVO.getWarehouseId())
                .eqIfPresent(ErpStockPositionDO::getCount, reqVO.getCount())
                .eqIfPresent(ErpStockPositionDO::getPositionId, reqVO.getPositionId())
                .betweenIfPresent(ErpStockPositionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ErpStockPositionDO::getId));
    }


    default BigDecimal selectSumByPosition(Long positionId, Long warehouseId, Long productId) {
        // SQL sum 查询
        List<Map<String, Object>> result = selectMaps(new QueryWrapper<ErpStockPositionDO>()
                .select("SUM(count) AS sumCount")
                .eq("product_id", productId)
                .eq("position_id", positionId)
                .eq("warehouse_id", warehouseId));
        // 获得数量
        if (CollUtil.isEmpty(result)) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(MapUtil.getDouble(result.get(0), "sumCount", 0D));
    }


    default List<ErpStockPositionRespVO> selectList(List<Long> ids) {
        return selectJoinList(ErpStockPositionRespVO.class,new MPJLambdaWrapper<ErpStockPositionDO>()
                .selectAll(ErpStockPositionDO.class)
                .selectAs(ErpProductDO::getName, "productName")
                .selectAs(ErpWarehouseDO::getName, "warehouseName")
                .selectAs(ErpWarehousePositionDO::getName, "positionName")
                .selectAs(ErpProductUnitDO::getName, "unitName")
                .leftJoin(ErpProductDO.class,ErpProductDO::getId,ErpStockPositionDO::getProductId)
                .leftJoin(ErpWarehouseDO.class,ErpWarehouseDO::getId,ErpStockPositionDO::getWarehouseId)
                .leftJoin(ErpWarehousePositionDO.class,ErpWarehousePositionDO::getId,ErpStockPositionDO::getPositionId)
                .leftJoin(ErpProductUnitDO.class,ErpProductUnitDO::getId,ErpProductDO::getUnitId)
                .in(ErpStockPositionDO::getProductId,ids));
    }


    default ErpStockPositionDO selectByProductIdAndWarehouseIdAndPositionId(Long productId, Long warehouseId, Long positionId) {
        return selectOne(ErpStockPositionDO::getProductId, productId,
                ErpStockPositionDO::getWarehouseId, warehouseId,
                ErpStockPositionDO::getPositionId, positionId);
    }

    default int updateCountIncrement(Long id, BigDecimal count, boolean negativeEnable) {
        LambdaUpdateWrapper<ErpStockPositionDO> updateWrapper = new LambdaUpdateWrapper<ErpStockPositionDO>()
                .eq(ErpStockPositionDO::getId, id);
        if (count.compareTo(BigDecimal.ZERO) > 0) {
            updateWrapper.setSql("count = count + " + count);
        } else if (count.compareTo(BigDecimal.ZERO) < 0) {
            if (!negativeEnable) {
                updateWrapper.ge(ErpStockPositionDO::getCount, count.abs());
            }
            updateWrapper.setSql("count = count - " + count.abs());
        }
        return update(null, updateWrapper);
    }
}