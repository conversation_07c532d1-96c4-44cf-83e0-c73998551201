package com.nj9394.module.erp.bpm;

import com.nj9394.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.nj9394.module.bpm.event.BpmProcessInstanceStatusEvent;
import com.nj9394.module.bpm.event.BpmProcessInstanceStatusEventListener;
import com.nj9394.module.erp.service.stock.ErpStockOutApplyService;
import com.nj9394.module.erp.service.stock.ErpStockOutApplyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ErpStockOutApplyStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    ErpStockOutApplyService erpStockOutApplyService;

    @Override
    protected String getProcessDefinitionKey() {
        return ErpStockOutApplyServiceImpl.PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        if(BpmProcessInstanceStatusEnum.APPROVE.getStatus().equals(event.getStatus())) {
            erpStockOutApplyService.updateBpmApprovedStatusByInstanceId(event.getId());
        }else if(BpmProcessInstanceStatusEnum.REJECT.getStatus().equals(event.getStatus())){
            erpStockOutApplyService.updateBpmRejectedStatusByInstanceId(event.getId());
        }
        log.info(event.toString());
    }
}
