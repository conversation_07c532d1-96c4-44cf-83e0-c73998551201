package com.nj9394.module.erp.dal.mysql.stockoutapply;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nj9394.module.erp.dal.dataobject.product.ErpProductDO;
import com.nj9394.module.erp.dal.dataobject.purchase.ErpPurchaseInDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyDO;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyItemDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.erp.controller.admin.stockoutapply.vo.*;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * ERP 领料申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StockOutApplyMapper extends BaseMapperX<StockOutApplyDO> {

    default PageResult<StockOutApplyDO> selectPage(ErpStockOutApplyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<StockOutApplyDO>()
                .eqIfPresent(StockOutApplyDO::getNo, reqVO.getNo())
                .eqIfPresent(StockOutApplyDO::getApplicant, reqVO.getApplicant())
                .betweenIfPresent(StockOutApplyDO::getOutTime, reqVO.getOutTime())
                .eqIfPresent(StockOutApplyDO::getStatus, reqVO.getStatus())
                .eqIfPresent(StockOutApplyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(StockOutApplyDO::getFileUrl, reqVO.getFileUrl())
                .betweenIfPresent(StockOutApplyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(StockOutApplyDO::getId));
    }

    default int updateByIdAndStatus(Long id, Integer status, StockOutApplyDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<StockOutApplyDO>()
                .eq(StockOutApplyDO::getId, id).eq(StockOutApplyDO::getStatus, status));
    }

    @Select(value = "SELECT " +
            "    t.id, " +
            "    t.NO, " +
            "    t.applicant, " +
            "   t3.nickname AS applicantName," +
            "    t.out_time, " +
            "    t.STATUS, " +
            "    t.remark, " +
            "    t.file_url, " +
            "    t.process_instance_id, " +
            "    t.create_time, " +
            "    t.update_time, " +
            "    t.creator, " +
            "    t.updater, " +
            "    t.deleted, " +
            "    GROUP_CONCAT( " +
            "        CONCAT(t2.NAME, ':', t1.count) " +
            "        ORDER BY t1.product_id " +
            "        SEPARATOR ', ' " +
            "    ) AS product_details " +
            " FROM " +
            "    erp_stock_out_apply t " +
            "    LEFT JOIN erp_stock_out_apply_item t1 ON t1.application_id = t.id " +
            "    AND t1.deleted = 0 " +
            "    LEFT JOIN erp_product t2 ON t2.id = t1.product_id " +
            "    AND t2.deleted = 0 " +
            " LEFT JOIN system_users t3 ON t3.id = t.applicant  " +
            " AND t3.deleted = 0 " +
            " WHERE " +
            "    t.deleted = 0 " +
            "    AND t.tenant_id = 1 " +
            "    AND t1.tenant_id = 1 " +
            " AND t3.tenant_id = 1 " +
            " GROUP BY " +
            "    t.id")
    Page<ErpStockOutApplyRespVO> selectItemsPage(Page<Object> page, ErpStockOutApplyPageReqVO reqVO);
}