package com.nj9394.module.erp.service.stockoutapply;

import com.nj9394.module.erp.service.stock.ErpStockOutApplyServiceImpl;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.erp.controller.admin.stockoutapply.vo.*;
import com.nj9394.module.erp.dal.dataobject.stockoutapply.StockOutApplyDO;
import com.nj9394.module.erp.dal.mysql.stockoutapply.StockOutApplyMapper;
import com.nj9394.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;

import static com.nj9394.module.erp.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ErpStockOutApplyServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ErpStockOutApplyServiceImpl.class)
public class ErpStockOutApplyServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ErpStockOutApplyServiceImpl stockOutApplyService;

    @Resource
    private StockOutApplyMapper stockOutApplyMapper;

    @Test
    public void testCreateStockOutApply_success() {
        // 准备参数
        ErpStockOutApplySaveReqVO createReqVO = randomPojo(ErpStockOutApplySaveReqVO.class).setId(null);

        // 调用
        Long stockOutApplyId = stockOutApplyService.createStockOutApply(createReqVO);
        // 断言
        assertNotNull(stockOutApplyId);
        // 校验记录的属性是否正确
        StockOutApplyDO stockOutApply = stockOutApplyMapper.selectById(stockOutApplyId);
        assertPojoEquals(createReqVO, stockOutApply, "id");
    }

    @Test
    public void testUpdateStockOutApply_success() {
        // mock 数据
        StockOutApplyDO dbStockOutApply = randomPojo(StockOutApplyDO.class);
        stockOutApplyMapper.insert(dbStockOutApply);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ErpStockOutApplySaveReqVO updateReqVO = randomPojo(ErpStockOutApplySaveReqVO.class, o -> {
            o.setId(dbStockOutApply.getId()); // 设置更新的 ID
        });

        // 调用
        stockOutApplyService.updateStockOutApply(updateReqVO);
        // 校验是否更新正确
        StockOutApplyDO stockOutApply = stockOutApplyMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, stockOutApply);
    }

    @Test
    public void testUpdateStockOutApply_notExists() {
        // 准备参数
        ErpStockOutApplySaveReqVO updateReqVO = randomPojo(ErpStockOutApplySaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> stockOutApplyService.updateStockOutApply(updateReqVO), STOCK_OUT_APPLY_NOT_EXISTS);
    }

    @Test
    public void testDeleteStockOutApply_success() {
        // mock 数据
        StockOutApplyDO dbStockOutApply = randomPojo(StockOutApplyDO.class);
        stockOutApplyMapper.insert(dbStockOutApply);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbStockOutApply.getId();

        // 调用
        stockOutApplyService.deleteStockOutApply(id);
       // 校验数据不存在了
       assertNull(stockOutApplyMapper.selectById(id));
    }

    @Test
    public void testDeleteStockOutApply_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> stockOutApplyService.deleteStockOutApply(id), STOCK_OUT_APPLY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetStockOutApplyPage() {
       // mock 数据
       StockOutApplyDO dbStockOutApply = randomPojo(StockOutApplyDO.class, o -> { // 等会查询到
           o.setNo(null);
           o.setApplicant(null);
           o.setOutTime(null);
           o.setStatus(null);
           o.setRemark(null);
           o.setFileUrl(null);
           o.setCreateTime(null);
       });
       stockOutApplyMapper.insert(dbStockOutApply);
       // 测试 no 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setNo(null)));
       // 测试 applicant 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setApplicant(null)));
       // 测试 outTime 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setOutTime(null)));
       // 测试 status 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setStatus(null)));
       // 测试 remark 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setRemark(null)));
       // 测试 fileUrl 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setFileUrl(null)));
       // 测试 createTime 不匹配
       stockOutApplyMapper.insert(cloneIgnoreId(dbStockOutApply, o -> o.setCreateTime(null)));
       // 准备参数
       ErpStockOutApplyPageReqVO reqVO = new ErpStockOutApplyPageReqVO();
       reqVO.setNo(null);
       reqVO.setApplicant(null);
       reqVO.setOutTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setStatus(null);
       reqVO.setRemark(null);
       reqVO.setFileUrl(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<StockOutApplyDO> pageResult = stockOutApplyService.getStockOutApplyPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbStockOutApply, pageResult.getList().get(0));
    }

}