# 视频流批处理优化方案

## 优化概述

### 原有问题
- 每个视频流启动独立Python进程
- 每个进程加载独立YOLO模型（内存消耗巨大）
- 线程池大小等于视频流数量
- 无法利用批处理优势

### 优化方案
- **单一Python进程**：启动一个批处理服务进程
- **批量处理**：收集多个视频流图片进行批量推理
- **队列机制**：使用队列收集待处理图片
- **固定线程池**：最多10个线程，不随视频流数量增长

## 核心文件

### 1. InitProjectApp.java (已修改)
- 使用PythonBatchService替代PythonService
- 固定线程池大小
- 启动批处理服务

### 2. PythonBatchService.java (新增)
- 管理单一Python批处理进程
- 视频流采集和队列管理
- 批量处理和结果分发

### 3. batchImageToResult.py (新增)
- 批量图片处理脚本
- 单一YOLO模型批量推理
- JSON通信协议

## 性能优势

1. **内存优化**：N个模型 → 1个模型
2. **GPU利用率**：单张推理 → 批量推理
3. **线程资源**：N个线程 → 最多10个线程
4. **整体吞吐量**：大幅提升

## 配置参数

```java
BATCH_SIZE = 8              // 批处理大小
BATCH_TIMEOUT_MS = 2000     // 批处理超时（毫秒）
FRAME_CAPTURE_INTERVAL = 1  // 帧采集间隔（秒）
```

## 兼容性

完全兼容原有功能：
- Redis数据结构不变
- 数据库记录格式不变
- 报警逻辑不变
- 图片保存路径不变

## 部署要求

1. FFmpeg（用于RTSP采集）
2. Python依赖：opencv-python, numpy
3. YOLO模型文件：model_het/yolov7x.onnx
