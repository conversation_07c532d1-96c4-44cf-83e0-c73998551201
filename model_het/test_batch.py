#!/usr/bin/env python3
"""
测试批处理脚本
用于验证批处理功能是否正常工作
"""
import json
import base64
import sys
import os
import cv2
import numpy as np

def create_test_image(width=640, height=480, text="Test"):
    """创建测试图片"""
    # 创建一个简单的测试图片
    image = np.zeros((height, width, 3), dtype=np.uint8)
    image[:] = (100, 150, 200)  # 填充颜色
    
    # 添加文本
    cv2.putText(image, text, (50, height//2), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    return image

def image_to_base64(image):
    """将图片转换为base64字符串"""
    _, buffer = cv2.imencode('.jpg', image)
    return base64.b64encode(buffer).decode('utf-8')

def test_batch_processing():
    """测试批处理"""
    print("开始测试批处理功能...", file=sys.stderr)
    
    # 创建测试图片
    images = []
    for i in range(3):
        img = create_test_image(text=f"Test {i+1}")
        img_b64 = image_to_base64(img)
        images.append({
            "stream_key": f"test_stream_{i+1}",
            "image_data": img_b64,
            "timestamp": 1234567890 + i
        })
    
    # 构建批处理请求
    request = {
        "batch_size": len(images),
        "images": images
    }
    
    # 输出请求
    print(json.dumps(request))
    sys.stdout.flush()
    
    # 模拟响应（实际应该从批处理脚本读取）
    response = {
        "batch_size": len(images),
        "results": [
            {
                "person_count": 2,
                "helmet_count": 1,
                "gap_person_and_helmet": 1,
                "image_base64": img_b64,
                "detection_results": [
                    {
                        "label": "person",
                        "score": 0.85,
                        "box": [100, 100, 200, 300]
                    },
                    {
                        "label": "helmet",
                        "score": 0.75,
                        "box": [150, 80, 180, 120]
                    }
                ]
            } for img_b64 in [image_to_base64(create_test_image(text=f"Result {i+1}")) for i in range(len(images))]
        ]
    }
    
    print("模拟响应:", file=sys.stderr)
    print(json.dumps(response, indent=2), file=sys.stderr)

if __name__ == "__main__":
    test_batch_processing()
