"""
Copyright (R) @huawei.com, all rights reserved
-*- coding:utf-8 -*-
CREATED:  2023-05-25 09:12:13
MODIFIED: 2023-05-25 10:10:55
"""
import os
import sys
import time
import numpy as np
import cv2
import base64
import json
import argparse
import platform

# 添加内置labels列表，包含人和安全帽类别
labels = ["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light",
         "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow",
         "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
         "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard",
         "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
         "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
         "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone",
         "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear",
         "hair drier", "toothbrush", "helmet"]  # 添加helmet类别

# 是否启用调试日志
DEBUG_MODE = False

def log_info(msg):
    """简单日志函数，只在DEBUG_MODE为True时输出"""
    if DEBUG_MODE:
        print(f"[INFO] {msg}")

def output_json(data):
    """输出JSON数据到标准输出，用于Java程序读取"""
    print(json.dumps(data, cls=NumpyEncoder))
    sys.stdout.flush()  # 确保输出被立即刷新

# 自定义JSON编码器，处理numpy数组
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)

class sample_YOLOV7_NMS_ONNX(object):
    def __init__(self, yolo_model_path, yolo_model_width, yolo_model_height):
        self.yolo_model_path = yolo_model_path  # string
        self.yolo_model = None
        self.yolo_model_width = yolo_model_width
        self.yolo_model_height = yolo_model_height
        self.yolo_result = None
        self.postprocess_result = None
        self.image = None
        self.resized_image = None
        # YOLOv7配置
        self.conf_threshold = 0.65
        self.nms_threshold = 0.45
        self.detection_results = []

    def init_resource(self):
        # 使用OpenCV DNN加载模型
        try:
            # 直接使用已有的ONNX模型路径
            log_info(f"加载YOLO模型: {self.yolo_model_path}")
            self.yolo_model = cv2.dnn.readNetFromONNX(self.yolo_model_path)
            log_info(f"加载YOLO模型成功")
        except Exception as e:
            log_info(f"加载模型失败: {str(e)}")
            raise

    def process_frame(self, frame):
        """处理单帧图像"""
        try:
            # 清空之前的结果
            self.detection_results = []

            # 调整图像大小为模型所需尺寸
            self.image = frame
            self.resized_image = cv2.resize(self.image, (self.yolo_model_width, self.yolo_model_height))

            # 预处理图像 - 使用OpenCV DNN blob
            self.blob = cv2.dnn.blobFromImage(self.resized_image, 1/255.0, (self.yolo_model_width, self.yolo_model_height),
                                              swapRB=True, crop=False)

            # 执行推理
            self.yolo_model.setInput(self.blob)
            outputs = self.yolo_model.forward(self.yolo_model.getUnconnectedOutLayersNames())
            self.yolo_result = outputs

            # 处理输出
            self.process_yolo_output()

            # 在图像上绘制结果
            result_image = self.draw_results_on_image()

            # 计算人数和安全帽数量
            person_count = self.count_objects("person")
            helmet_count = self.count_objects("helmet")
            gap_person_and_helmet = person_count - helmet_count

            # 返回结果，只包含Java端需要的数据
            return {
                "person_count": person_count,
                "helmet_count": helmet_count,
                "gap_person_and_helmet": gap_person_and_helmet,
                "result_image": result_image,
                # 只在检测到人且未佩戴安全帽时返回详细检测结果
                "detection_results": self.detection_results if gap_person_and_helmet > 0 else []
            }
        except Exception as e:
            log_info(f"处理图像帧失败: {str(e)}")
            return None

    def process_yolo_output(self):
        """处理YOLOv7输出并执行NMS"""
        try:
            # 获取输出张量（假设使用YOLOv7输出格式）
            outputs = self.yolo_result[0]  # 形状应该是 [1, num_boxes, 85]，85 = 4(box) + 1(conf) + 80(classes)

            # 创建列表存储检测结果
            boxes = []
            confidences = []
            class_ids = []

            # 解析检测结果
            rows = outputs.shape[1]
            image_width, image_height = self.image.shape[1], self.image.shape[0]
            x_factor = image_width / self.yolo_model_width
            y_factor = image_height / self.yolo_model_height

            for r in range(rows):
                row = outputs[0, r]
                confidence = row[4]

                # 只处理置信度高于阈值的检测
                if confidence >= self.conf_threshold:
                    classes_scores = row[5:]
                    class_id = np.argmax(classes_scores)

                    # 如果类别置信度也高于阈值
                    if classes_scores[class_id] >= self.conf_threshold:
                        cx, cy, w, h = row[0], row[1], row[2], row[3]

                        # 转换为图像坐标系中的边界框坐标
                        left = int((cx - w/2) * x_factor)
                        top = int((cy - h/2) * y_factor)
                        width = int(w * x_factor)
                        height = int(h * y_factor)

                        # 添加到检测结果
                        boxes.append([left, top, width, height])
                        confidences.append(float(confidence))
                        class_ids.append(int(class_id))

            # 非极大值抑制
            indices = cv2.dnn.NMSBoxes(boxes, confidences, self.conf_threshold, self.nms_threshold)

            # 清空之前的结果
            self.detection_results = []

            # 处理检测结果
            for i in indices:
                if isinstance(i, list):  # OpenCV 3.x returns list of lists
                    i = i[0]
                box = boxes[i]
                left, top, width, height = box
                class_id = class_ids[i]
                confidence = confidences[i]

                # 添加到结果列表
                self.detection_results.append({
                    "label": labels[class_id],
                    "score": float(confidence),
                    "box": [left, top, left + width, top + height]
                })

            # 只在调试模式下输出日志
            log_info(f"后处理完成，检测到 {len(self.detection_results)} 个目标")
        except Exception as e:
            log_info(f"后处理失败: {str(e)}")
            self.detection_results = []

    def draw_results_on_image(self):
        """在图像上绘制检测结果"""
        try:
            # 复制原始图像
            result_image = self.image.copy()

            # 绘制每个检测框
            for detection in self.detection_results:
                label = detection["label"]
                score = detection["score"]
                box = detection["box"]

                # 绘制边界框
                cv2.rectangle(result_image,
                             (int(box[0]), int(box[1])),
                             (int(box[2]), int(box[3])),
                             (0, 0, 255), 2)

                # 绘制标签
                text = f"{label}: {score:.2f}"
                cv2.putText(result_image, text,
                           (int(box[0]), int(box[1]) - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 添加人数和安全帽数量信息
            person_count = self.count_objects("person")
            helmet_count = self.count_objects("helmet")
            gap = person_count - helmet_count

            cv2.putText(result_image, f"person: {person_count}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(result_image, f"hat: {helmet_count}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
#             cv2.putText(result_image, f"noHet: {gap}", (10, 110),
#                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            return result_image
        except Exception as e:
            log_info(f"绘制结果失败: {str(e)}")
            return self.image

    def count_objects(self, label_name):
        """计算特定类别对象的数量"""
        return sum(1 for detection in self.detection_results if detection["label"] == label_name)

    def release_resource(self):
        """释放资源"""
        self.yolo_model = None
        # 手动触发垃圾回收
        import gc
        gc.collect()
        log_info("资源已释放")

def process_rtsp_stream(rtsp_url, output_dir=None, frame_limit=1, continuous_mode=False, interval=5, minimal=False, redis_key=None):
    """处理RTSP视频流并返回分析结果

    Args:
        rtsp_url: RTSP视频流地址
        output_dir: 输出目录，如果为None则使用默认路径
        frame_limit: 非持续模式下处理的帧数限制，持续模式下此参数无效
        continuous_mode: 是否持续监控模式，如果为True，则会每隔interval秒处理一次最新帧
        interval: 持续监控模式下的处理间隔时间（秒），实际等待时间会减去处理帧所需的时间
        minimal: 是否启用精简模式，只输出必要的JSON数据
        redis_key: Redis键，用于创建子目录

    Returns:
        分析结果字典

    注意:
        在持续监控模式下，程序会在每次处理前彻底清空缓冲区，确保处理的是最新的帧。
        清空缓冲区的方法有两种：
        1. 设置OpenCV的CAP_PROP_BUFFERSIZE为1
        2. 循环读取所有可用帧直到没有更多帧或达到时间限制（最多0.5秒）
        这样可以避免处理延迟累积，确保每次处理的都是最新帧。
    """
    # 根据操作系统设置固定的输出根目录
    if platform.system() == "Windows":
        base_output_dir = "D:\\data\\outHet"
    else:
        base_output_dir = "/data/outHet"

    # 如果提供了redis_key，则创建对应的子目录
    if redis_key:
        # 替换可能在文件路径中引起问题的字符
        safe_key = redis_key.replace(":", "_").replace("/", "_")
        output_dir = os.path.join(base_output_dir, safe_key)
    else:
        output_dir = base_output_dir

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    log_info(f"设置输出目录为: {output_dir}")

    # 初始化模型
    current_dir = os.path.dirname(os.path.abspath(__file__))
    yolo_model_path = os.path.join(current_dir, "yolov7x.onnx")
    if not os.path.exists(yolo_model_path):
        error_msg = {"error": f"YOLO模型文件不存在: {yolo_model_path}"}
        output_json(error_msg)
        return error_msg

    # 初始化模型
    yolo_width = 640
    yolo_height = 640
    net = sample_YOLOV7_NMS_ONNX(yolo_model_path, yolo_width, yolo_height)

    try:
        net.init_resource()
        log_info("模型初始化成功")
    except Exception as e:
        error_msg = {"error": f"模型初始化失败: {str(e)}"}
        output_json(error_msg)
        return error_msg

    try:
        # 打开RTSP流
        log_info(f"尝试连接RTSP流: {rtsp_url}")
        cap = cv2.VideoCapture(rtsp_url)

        # 设置缓冲区大小为1，尽量只保留最新帧
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        if not cap.isOpened():
            error_msg = {"error": f"无法连接到RTSP流: {rtsp_url}"}
            output_json(error_msg)
            return error_msg

        log_info(f"成功连接到RTSP流: {rtsp_url}")

        all_results = []
        frame_count = 0

        # 持续监控模式
        try:
            while True:
                if not continuous_mode and frame_count >= frame_limit:
                    break

                # 在持续模式下，彻底清空缓冲区，确保读取最新帧
                if continuous_mode:
                    # 彻底清空缓冲区，读取最新的帧
                    # 方法1：循环读取直到没有更多帧
                    last_frame = None
                    last_ret = False

                    # 持续读取帧直到没有更多帧或达到时间限制
                    buffer_clear_start = time.time()
                    frames_cleared = 0
                    max_clear_time = 0.5  # 最多花0.5秒清空缓冲区

                    while time.time() - buffer_clear_start < max_clear_time:
                        ret, frame = cap.read()
                        if not ret:
                            break
                        last_ret = ret
                        last_frame = frame
                        frames_cleared += 1

                    log_info(f"清空了 {frames_cleared} 帧")

                    # 如果成功读取了至少一帧，使用最后一帧作为当前帧
                    if last_ret and last_frame is not None:
                        ret, frame = last_ret, last_frame
                    else:
                        # 如果没有读取到帧，尝试正常读取
                        ret, frame = cap.read()
                else:
                    # 非持续模式，正常读取
                    ret, frame = cap.read()

                if not ret:
                    log_info("无法读取视频帧，尝试重新连接...")
                    # 尝试重新连接
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(rtsp_url)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 重新设置缓冲区大小
                    if not cap.isOpened():
                        log_info("重新连接失败，退出")
                        break
                    continue

                # 处理帧
                start_time = time.time()
                result = net.process_frame(frame)
                end_time = time.time()
                processing_time = end_time - start_time

                if result:
                    # 添加处理时间和时间戳
                    result["processing_time"] = processing_time
                    result["timestamp"] = time.time()
                    result["frame_count"] = frame_count  # 添加帧号，方便Java端处理

                    # 获取检测结果
                    person_count = result["person_count"]
                    helmet_count = result["helmet_count"]
                    gap = result["gap_person_and_helmet"]

                    if output_dir:
                        output_path = os.path.join(output_dir, f"result_{frame_count}.jpg")
                        cv2.imwrite(output_path, result["result_image"])
                        log_info(f"检测到未佩戴安全帽情况，结果图像已保存到: {output_path}")

                        # 转换为Base64
                        with open(output_path, 'rb') as image_file:
                            image_data = image_file.read()
                        result["image_base64"] = base64.b64encode(image_data).decode('utf-8')

                    # 删除numpy数组以便JSON序列化
                    if "result_image" in result:
                        del result["result_image"]

                    # 在精简模式下，移除不必要的数据
                    if minimal and "detection_results" in result and gap <= 0:
                        del result["detection_results"]

                    # 添加到结果列表
                    all_results.append(result)

                    log_info(f"帧 {frame_count}: 人数={person_count}, 安全帽数={helmet_count}, 未佩戴安全帽人数={gap}, 处理时间={processing_time:.3f}秒")

                    # 在持续模式下，每次处理完输出一次结果
                    if continuous_mode:
                        # 创建当前帧的汇总结果，只包含必要信息
                        summary = {
                            "frame_count": frame_count,
                            "timestamp": time.time(),
                            "person_count": person_count,
                            "helmet_count": helmet_count,
                            "gap_person_and_helmet": gap,
                        }

                        # 无论是否为精简模式，都添加image_base64字段
                        if "image_base64" in result:
                            summary["image_base64"] = result["image_base64"]

                        # 在非精简模式下添加详细检测结果
                        if not minimal:
                            summary["detection_results"] = result["detection_results"]

                        # 输出JSON结果
                        output_json(summary)

                frame_count += 1

                # 在持续模式下，等待指定的间隔时间
                if continuous_mode:
                    # 计算需要等待的时间（考虑处理时间）
                    wait_time = max(0, interval - processing_time)
                    if wait_time > 0:
                        time.sleep(wait_time)

        except KeyboardInterrupt:
            log_info("接收到中断信号，停止处理")

        # 非持续模式下，汇总所有结果
        if not continuous_mode:
            # 计算总人数和安全帽数量
            total_person_count = sum(result["person_count"] for result in all_results)
            total_helmet_count = sum(result["helmet_count"] for result in all_results)
            gap_person_and_helmet = total_person_count - total_helmet_count

            # 只保留必要的汇总信息，减少数据量
            summary = {
                "frame_count": frame_count - 1,  # 最后处理的帧号
                "total_frames_processed": frame_count,
                "total_person_count": total_person_count,
                "total_helmet_count": total_helmet_count,
                "gap_person_and_helmet": gap_person_and_helmet
            }

            # 只有在有未佩戴安全帽的情况下才添加详细结果
            if gap_person_and_helmet > 0:
                # 只保留未佩戴安全帽的帧的结果
                unsafe_frames = [r for r in all_results if r["gap_person_and_helmet"] > 0]
                if unsafe_frames and not minimal:
                    # 在非精简模式下添加完整的不安全帧信息
                    summary["unsafe_frames"] = unsafe_frames
                elif unsafe_frames and minimal:
                    # 在精简模式下只添加必要的不安全帧信息
                    minimal_frames = []
                    for frame in unsafe_frames:
                        minimal_frame = {
                            "frame_count": frame.get("frame_count", 0),
                            "person_count": frame.get("person_count", 0),
                            "helmet_count": frame.get("helmet_count", 0),
                            "gap_person_and_helmet": frame.get("gap_person_and_helmet", 0)
                        }
                        # 添加base64编码的图像数据
                        if "image_base64" in frame:
                            minimal_frame["image_base64"] = frame["image_base64"]
                        minimal_frames.append(minimal_frame)
                    summary["unsafe_frames"] = minimal_frames

            log_info(f"总共含有 {total_person_count} 个人")
            log_info(f"总共含有 {total_helmet_count} 个安全帽")
            log_info(f"未佩戴安全帽人数: {gap_person_and_helmet}")

            # 输出最终结果
            output_json(summary)
            return summary

        return {"message": "持续监控模式已完成"}

    except Exception as e:
        error_msg = {"error": f"处理视频流时出错: {str(e)}"}
        output_json(error_msg)
        return error_msg

    finally:
        if 'cap' in locals() and cap is not None:
            cap.release()
        if 'net' in locals() and net is not None:
            net.release_resource()

def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description="安全帽检测程序")
    parser.add_argument("--rtsp", required=True, help="RTSP视频流地址")
    parser.add_argument("--output", default=None, help="输出目录")
    parser.add_argument("--frames", type=int, default=1, help="非持续模式下处理的帧数限制")
    parser.add_argument("--continuous", action="store_true", help="持续监控模式，每次处理最新的一帧")
    parser.add_argument("--interval", type=int, default=1, help="持续监控模式下的处理间隔时间（秒）")
    parser.add_argument("--debug", action="store_true", help="启用调试模式，输出更多日志")
    parser.add_argument("--minimal", action="store_true", help="启用精简模式，只输出必要的JSON数据")
    parser.add_argument("--redis-key", default=None, help="Redis键，用于创建子目录")
    args = parser.parse_args()

    # 设置调试模式
    global DEBUG_MODE
    DEBUG_MODE = args.debug

    # 处理视频流
    process_rtsp_stream(
        args.rtsp,
        args.output,
        args.frames,
        args.continuous,
        args.interval,
        args.minimal,
        args.redis_key
    )

    # 在非持续模式下，输出最终结果由process_rtsp_stream函数处理

if __name__ == "__main__":
    main()
