"""
Copyright (R) @huawei.com, all rights reserved
-*- coding:utf-8 -*-
CREATED:  2023-05-25 09:12:13
MODIFIED: 2023-05-25 10:10:55
"""
import os
import sys
import time
import numpy as np
import cv2
import base64
import json
import argparse
import platform
import logging
from datetime import datetime

# 尝试导入onnxruntime
try:
    import onnxruntime as ort
    HAVE_ORT = True
    # 检查是否有GPU支持
    if 'CUDAExecutionProvider' in ort.get_available_providers():
        ORT_HAVE_CUDA = True
    else:
        ORT_HAVE_CUDA = False
except ImportError:
    HAVE_ORT = False
    ORT_HAVE_CUDA = False

# 尝试导入torch
try:
    import torch
    HAVE_TORCH = True
except ImportError:
    HAVE_TORCH = False

# 添加内置labels列表，包含人和安全帽类别
labels = ["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light",
         "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow",
         "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
         "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard",
         "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
         "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
         "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone",
         "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear",
         "hair drier", "toothbrush", "helmet"]  # 添加helmet类别

# 是否启用调试日志
DEBUG_MODE = False

# 设置日志
LOG_DIR = "/home/<USER>/work/model_het/log"
os.makedirs(LOG_DIR, exist_ok=True)
log_file = os.path.join(LOG_DIR, f"model_het_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("model_het")

# 检查GPU可用性
def check_gpu():
    """检查GPU是否可用"""
    gpu_available = False
    
    # 检查PyTorch GPU
    if HAVE_TORCH and torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        device_names = [torch.cuda.get_device_name(i) for i in range(device_count)]
        logger.info(f"PyTorch找到 {device_count} 个GPU设备: {device_names}")
        gpu_available = True
    
    # 检查ONNX Runtime GPU
    if HAVE_ORT:
        providers = ort.get_available_providers()
        logger.info(f"ONNX Runtime可用提供程序: {providers}")
        if 'CUDAExecutionProvider' in providers:
            logger.info("ONNX Runtime支持CUDA")
            gpu_available = True
    
    if not gpu_available:
        logger.warning("未检测到可用的GPU设备，将使用CPU")
    
    return gpu_available

# 初始化GPU检查
GPU_AVAILABLE = check_gpu()

def log_info(msg):
    """简单日志函数，同时输出到控制台和文件"""
    if DEBUG_MODE:
        print(f"[INFO] {msg}")
    logger.info(msg)

def output_json(data):
    """输出JSON数据到标准输出，用于Java程序读取"""
    print(json.dumps(data, cls=NumpyEncoder))
    sys.stdout.flush()  # 确保输出被立即刷新

# 自定义JSON编码器，处理numpy数组
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)

class sample_YOLOV7_NMS_ONNX(object):
    def __init__(self, yolo_model_path, yolo_model_width, yolo_model_height):
        self.yolo_model_path = yolo_model_path  # string
        self.yolo_model = None
        self.sess = None  # ONNX Runtime会话
        self.yolo_model_width = yolo_model_width
        self.yolo_model_height = yolo_model_height
        self.yolo_result = None
        self.postprocess_result = None
        self.image = None
        self.resized_image = None
        # YOLOv7配置
        self.conf_threshold = 0.65
        self.nms_threshold = 0.45
        self.detection_results = []
        # 指示使用哪种推理后端
        self.use_onnx = False
        self.use_opencv = False
        self.input_name = None
        self.output_names = None

    def init_resource(self, use_cpu=False):
        # 在Linux系统上强制使用GPU，在其他系统上根据参数决定
        force_gpu = platform.system() == "Linux"
        use_cpu = use_cpu and not force_gpu
        
        if force_gpu:
            log_info("在Linux系统上强制使用GPU执行模型")
        
        # 首先尝试使用ONNX Runtime加载模型
        if HAVE_ORT:
            try:
                log_info(f"使用ONNX Runtime加载模型: {self.yolo_model_path}")
                
                # 配置ONNX Runtime会话选项
                sess_options = ort.SessionOptions()
                sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
                
                # 根据参数和环境选择执行提供程序
                providers = []
                if not use_cpu and ORT_HAVE_CUDA:
                    # 使用CUDA
                    log_info("配置ONNX Runtime使用CUDA执行提供程序")
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                else:
                    # 使用CPU
                    log_info("配置ONNX Runtime使用CPU执行提供程序")
                    providers = ['CPUExecutionProvider']
                
                # 创建推理会话
                self.sess = ort.InferenceSession(self.yolo_model_path, sess_options, providers=providers)
                
                # 获取模型输入输出信息
                self.input_name = self.sess.get_inputs()[0].name
                self.output_names = [output.name for output in self.sess.get_outputs()]
                
                # 记录实际使用的提供程序
                actual_providers = self.sess.get_providers()
                log_info(f"ONNX Runtime实际使用的执行提供程序: {actual_providers}")
                
                self.use_onnx = True
                self.use_opencv = False
                log_info("使用ONNX Runtime加载模型成功")
                return
            except Exception as e:
                log_info(f"ONNX Runtime加载模型失败: {str(e)}")
                log_info("将尝试使用OpenCV DNN作为备选方案")
        else:
            log_info("未找到ONNX Runtime，将尝试使用OpenCV DNN")
        
        # 如果ONNX Runtime不可用或加载失败，尝试使用OpenCV DNN
        try:
            log_info(f"使用OpenCV DNN加载模型: {self.yolo_model_path}")
            self.yolo_model = cv2.dnn.readNetFromONNX(self.yolo_model_path)
            
            # 在Linux上强制使用GPU
            if force_gpu and GPU_AVAILABLE:
                log_info("配置OpenCV DNN使用CUDA后端")
                self.yolo_model.setPreferableBackend(cv2.dnn.DNN_BACKEND_CUDA)
                self.yolo_model.setPreferableTarget(cv2.dnn.DNN_TARGET_CUDA)
                log_info("OpenCV DNN已配置为使用CUDA后端")
            elif not use_cpu:
                # 尝试使用GPU但不强制
                try:
                    self.yolo_model.setPreferableBackend(cv2.dnn.DNN_BACKEND_CUDA)
                    self.yolo_model.setPreferableTarget(cv2.dnn.DNN_TARGET_CUDA)
                    log_info("OpenCV DNN已配置为使用CUDA后端")
                except Exception as e:
                    log_info(f"配置CUDA后端失败，将使用CPU: {str(e)}")
                    self.yolo_model.setPreferableBackend(cv2.dnn.DNN_BACKEND_DEFAULT)
                    self.yolo_model.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
            else:
                # 强制使用CPU
                log_info("根据参数设置，使用CPU进行推理")
                self.yolo_model.setPreferableBackend(cv2.dnn.DNN_BACKEND_DEFAULT)
                self.yolo_model.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
            
            self.use_opencv = True
            self.use_onnx = False
            log_info(f"使用OpenCV DNN加载YOLO模型成功")
        except Exception as e:
            log_info(f"OpenCV DNN加载模型失败: {str(e)}")
            raise

    def process_frame(self, frame):
        """处理单帧图像"""
        try:
            # 清空之前的结果
            self.detection_results = []

            # 调整图像大小为模型所需尺寸
            self.image = frame
            self.resized_image = cv2.resize(self.image, (self.yolo_model_width, self.yolo_model_height))

            start_time = time.time()
            
            # 根据加载的模型类型选择不同的推理方法
            if self.use_onnx:
                # 使用ONNX Runtime进行推理
                # 预处理图像 - ONNX Runtime输入格式
                input_img = cv2.cvtColor(self.resized_image, cv2.COLOR_BGR2RGB)
                input_img = input_img.transpose(2, 0, 1)  # HWC to CHW
                input_img = np.ascontiguousarray(input_img)
                input_img = input_img.astype(np.float32) / 255.0  # 归一化
                
                # 执行推理
                outputs = self.sess.run(self.output_names, {self.input_name: input_img[np.newaxis, ...]})
                self.yolo_result = outputs
            else:
                # 使用OpenCV DNN进行推理
                # 预处理图像
                self.blob = cv2.dnn.blobFromImage(self.resized_image, 1/255.0, 
                                                (self.yolo_model_width, self.yolo_model_height),
                                                swapRB=True, crop=False)
                
                # 设置模型输入并执行推理
                self.yolo_model.setInput(self.blob)
                outputs = self.yolo_model.forward(self.yolo_model.getUnconnectedOutLayersNames())
                self.yolo_result = outputs
            
            inference_time = time.time() - start_time
            log_info(f"模型推理耗时: {inference_time:.3f}秒")

            # 处理输出
            self.process_yolo_output()

            # 在图像上绘制结果
            result_image = self.draw_results_on_image()

            # 计算人数和安全帽数量
            person_count = self.count_objects("person")
            helmet_count = self.count_objects("helmet")
            gap_person_and_helmet = person_count - helmet_count

            # 返回结果，只包含Java端需要的数据
            return {
                "person_count": person_count,
                "helmet_count": helmet_count,
                "gap_person_and_helmet": gap_person_and_helmet,
                "result_image": result_image,
                # 只在检测到人且未佩戴安全帽时返回详细检测结果
                "detection_results": self.detection_results if gap_person_and_helmet > 0 else []
            }
        except Exception as e:
            log_info(f"处理图像帧失败: {str(e)}")
            return None

    def process_yolo_output(self):
        """处理YOLOv7输出并执行NMS"""
        try:
            # 获取输出张量
            if self.use_onnx:
                # ONNX Runtime输出可能有多个
                if len(self.yolo_result) == 1:
                    # 单输出模型
                    outputs = self.yolo_result[0]
                else:
                    # 多输出模型，需要找到包含检测结果的输出
                    # 通常是形状为[1, N, 85]或[N, 85]的张量
                    for output in self.yolo_result:
                        if len(output.shape) == 3 and output.shape[2] > 5:  # [1, N, 85+]
                            outputs = output[0]  # 取第一个批次
                            break
                        elif len(output.shape) == 2 and output.shape[1] > 5:  # [N, 85+]
                            outputs = output
                            break
                    else:
                        # 如果没有找到合适的输出，使用第一个
                        outputs = self.yolo_result[0]
                        if len(outputs.shape) == 3:
                            outputs = outputs[0]
            else:
                # OpenCV DNN的输出格式
                outputs = self.yolo_result[0]
            
            # 确保输出是2D数组 [num_boxes, num_classes+5]
            if len(outputs.shape) == 3:  # 如果是[1, num_boxes, num_classes+5]
                outputs = outputs[0]  # 变成[num_boxes, num_classes+5]

            # 创建列表存储检测结果
            boxes = []
            confidences = []
            class_ids = []

            # 解析检测结果
            image_width, image_height = self.image.shape[1], self.image.shape[0]
            x_factor = image_width / self.yolo_model_width
            y_factor = image_height / self.yolo_model_height

            # 遍历所有检测结果
            for detection in outputs:
                confidence = float(detection[4])

                # 只处理置信度高于阈值的检测
                if confidence >= self.conf_threshold:
                    classes_scores = detection[5:]
                    class_id = np.argmax(classes_scores)
                    class_score = float(classes_scores[class_id])

                    # 如果类别置信度也高于阈值
                    if class_score >= self.conf_threshold:
                        cx, cy, w, h = detection[0], detection[1], detection[2], detection[3]

                        # 转换为图像坐标系中的边界框坐标
                        left = int((cx - w/2) * x_factor)
                        top = int((cy - h/2) * y_factor)
                        width = int(w * x_factor)
                        height = int(h * y_factor)

                        # 添加到检测结果
                        boxes.append([left, top, width, height])
                        confidences.append(confidence * class_score)  # 组合置信度
                        class_ids.append(int(class_id))

            # 非极大值抑制
            if boxes:  # 确保有检测结果
                indices = cv2.dnn.NMSBoxes(boxes, confidences, self.conf_threshold, self.nms_threshold)
            else:
                indices = []

            # 清空之前的结果
            self.detection_results = []

            # 处理检测结果
            for i in indices:
                if isinstance(i, (list, np.ndarray)):  # OpenCV 3.x returns list of lists
                    i = i[0]
                box = boxes[i]
                left, top, width, height = box
                class_id = class_ids[i]
                confidence = confidences[i]

                # 确保class_id在有效范围内
                if class_id < len(labels):
                    label = labels[class_id]
                else:
                    label = f"class{class_id}"

                # 添加到结果列表
                self.detection_results.append({
                    "label": label,
                    "score": float(confidence),
                    "box": [left, top, left + width, top + height]
                })

            log_info(f"后处理完成，检测到 {len(self.detection_results)} 个目标")
        except Exception as e:
            log_info(f"后处理失败: {str(e)}")
            self.detection_results = []

    def draw_results_on_image(self):
        """在图像上绘制检测结果"""
        try:
            # 复制原始图像
            result_image = self.image.copy()

            # 绘制每个检测框
            for detection in self.detection_results:
                label = detection["label"]
                score = detection["score"]
                box = detection["box"]

                # 绘制边界框
                cv2.rectangle(result_image,
                             (int(box[0]), int(box[1])),
                             (int(box[2]), int(box[3])),
                             (0, 0, 255), 2)

                # 绘制标签
                text = f"{label}: {score:.2f}"
                cv2.putText(result_image, text,
                           (int(box[0]), int(box[1]) - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 添加人数和安全帽数量信息
            person_count = self.count_objects("person")
            helmet_count = self.count_objects("helmet")
            gap = person_count - helmet_count

            cv2.putText(result_image, f"person: {person_count}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(result_image, f"hat: {helmet_count}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
#             cv2.putText(result_image, f"noHet: {gap}", (10, 110),
#                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            return result_image
        except Exception as e:
            log_info(f"绘制结果失败: {str(e)}")
            return self.image

    def count_objects(self, label_name):
        """计算特定类别对象的数量"""
        return sum(1 for detection in self.detection_results if detection["label"] == label_name)

    def release_resource(self):
        """释放资源"""
        # 释放模型资源
        if self.use_onnx:
            self.sess = None
            log_info("ONNX Runtime会话已释放")
        else:
            self.yolo_model = None
            log_info("OpenCV DNN模型已释放")
        
        # 手动触发垃圾回收
        import gc
        gc.collect()
        log_info("所有资源已释放")

def process_rtsp_stream(rtsp_url, output_dir=None, frame_limit=1, continuous_mode=False, interval=5, minimal=False, redis_key=None, use_cpu=False):
    """处理RTSP视频流并返回分析结果

    Args:
        rtsp_url: RTSP视频流地址
        output_dir: 输出目录，如果为None则使用默认路径
        frame_limit: 非持续模式下处理的帧数限制，持续模式下此参数无效
        continuous_mode: 是否持续监控模式，如果为True，则会每隔interval秒处理一次最新帧
        interval: 持续监控模式下的处理间隔时间（秒），实际等待时间会减去处理帧所需的时间
        minimal: 是否启用精简模式，只输出必要的JSON数据
        redis_key: Redis键，用于创建子目录
        use_cpu: 是否强制使用CPU进行推理，即使CUDA可用

    Returns:
        分析结果字典

    注意:
        在持续监控模式下，程序会在每次处理前彻底清空缓冲区，确保处理的是最新的帧。
        清空缓冲区的方法有两种：
        1. 设置OpenCV的CAP_PROP_BUFFERSIZE为1
        2. 循环读取所有可用帧直到没有更多帧或达到时间限制（最多0.5秒）
        这样可以避免处理延迟累积，确保每次处理的都是最新帧。
    """
    # 根据操作系统设置固定的输出根目录
    # 首先检查环境变量中是否设置了输出目录
    custom_output_dir = os.environ.get('OUTPUT_DIR')
    if custom_output_dir:
        base_output_dir = custom_output_dir
        log_info(f"使用环境变量设置的输出目录: {base_output_dir}")
    elif platform.system() == "Windows":
        base_output_dir = "D:\\data\\outHet"
    else:
        # Linux系统默认使用用户home目录
        user_home = os.path.expanduser("~")
        base_output_dir = f"{user_home}/data/outHet"
    
    # 确保根目录存在
    os.makedirs(base_output_dir, exist_ok=True)
    log_info(f"使用根输出目录: {base_output_dir}")

    # 如果提供了redis_key，则创建对应的子目录
    if redis_key:
        # 替换可能在文件路径中引起问题的字符
        safe_key = redis_key.replace(":", "_").replace("/", "_")
        output_dir = os.path.join(base_output_dir, safe_key)
    else:
        output_dir = base_output_dir

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    log_info(f"设置输出目录为: {output_dir}")

    # 初始化模型
    current_dir = os.path.dirname(os.path.abspath(__file__))
    yolo_model_path = os.path.join(current_dir, "yolov7x.onnx")
    if not os.path.exists(yolo_model_path):
        error_msg = {"error": f"YOLO模型文件不存在: {yolo_model_path}"}
        output_json(error_msg)
        return error_msg

    # 初始化模型
    yolo_width = 640
    yolo_height = 640
    net = sample_YOLOV7_NMS_ONNX(yolo_model_path, yolo_width, yolo_height)
    
    # 如果指定了使用CPU，记录日志
    if use_cpu:
        log_info("根据参数设置，将强制使用CPU进行推理")

    try:
        # 传递use_cpu参数到init_resource方法
        net.init_resource(use_cpu)
        log_info("模型初始化成功")
    except Exception as e:
        error_msg = {"error": f"模型初始化失败: {str(e)}"}
        output_json(error_msg)
        return error_msg

    try:
        # 打开RTSP流
        log_info(f"尝试连接RTSP流: {rtsp_url}")
        cap = cv2.VideoCapture(rtsp_url)

        # 设置缓冲区大小为1，尽量只保留最新帧
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        if not cap.isOpened():
            error_msg = {"error": f"无法连接到RTSP流: {rtsp_url}"}
            output_json(error_msg)
            return error_msg

        log_info(f"成功连接到RTSP流: {rtsp_url}")

        all_results = []
        frame_count = 0

        # 持续监控模式
        try:
            while True:
                if not continuous_mode and frame_count >= frame_limit:
                    break

                # 在持续模式下，彻底清空缓冲区，确保读取最新帧
                if continuous_mode:
                    # 彻底清空缓冲区，读取最新的帧
                    # 方法1：循环读取直到没有更多帧
                    last_frame = None
                    last_ret = False

                    # 持续读取帧直到没有更多帧或达到时间限制
                    buffer_clear_start = time.time()
                    frames_cleared = 0
                    max_clear_time = 0.5  # 最多花0.5秒清空缓冲区

                    while time.time() - buffer_clear_start < max_clear_time:
                        ret, frame = cap.read()
                        if not ret:
                            break
                        last_ret = ret
                        last_frame = frame
                        frames_cleared += 1

                    log_info(f"清空了 {frames_cleared} 帧")

                    # 如果成功读取了至少一帧，使用最后一帧作为当前帧
                    if last_ret and last_frame is not None:
                        ret, frame = last_ret, last_frame
                    else:
                        # 如果没有读取到帧，尝试正常读取
                        ret, frame = cap.read()
                else:
                    # 非持续模式，正常读取
                    ret, frame = cap.read()

                if not ret:
                    log_info("无法读取视频帧，尝试重新连接...")
                    # 尝试重新连接
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(rtsp_url)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 重新设置缓冲区大小
                    if not cap.isOpened():
                        log_info("重新连接失败，退出")
                        break
                    continue

                # 处理帧
                start_time = time.time()
                result = net.process_frame(frame)
                end_time = time.time()
                processing_time = end_time - start_time

                if result:
                    # 添加处理时间和时间戳
                    result["processing_time"] = processing_time
                    result["timestamp"] = time.time()
                    result["frame_count"] = frame_count  # 添加帧号，方便Java端处理

                    # 获取检测结果
                    person_count = result["person_count"]
                    helmet_count = result["helmet_count"]
                    gap = result["gap_person_and_helmet"]

                    if output_dir:
                        output_path = os.path.join(output_dir, f"result_{frame_count}.jpg")
                        cv2.imwrite(output_path, result["result_image"])
                        log_info(f"检测到未佩戴安全帽情况，结果图像已保存到: {output_path}")

                        # 转换为Base64
                        with open(output_path, 'rb') as image_file:
                            image_data = image_file.read()
                        result["image_base64"] = base64.b64encode(image_data).decode('utf-8')

                    # 删除numpy数组以便JSON序列化
                    if "result_image" in result:
                        del result["result_image"]

                    # 在精简模式下，移除不必要的数据
                    if minimal and "detection_results" in result and gap <= 0:
                        del result["detection_results"]

                    # 添加到结果列表
                    all_results.append(result)

                    log_info(f"帧 {frame_count}: 人数={person_count}, 安全帽数={helmet_count}, 未佩戴安全帽人数={gap}, 处理时间={processing_time:.3f}秒")

                    # 在持续模式下，每次处理完输出一次结果
                    if continuous_mode:
                        # 创建当前帧的汇总结果，只包含必要信息
                        summary = {
                            "frame_count": frame_count,
                            "timestamp": time.time(),
                            "person_count": person_count,
                            "helmet_count": helmet_count,
                            "gap_person_and_helmet": gap,
                        }

                        # 无论是否为精简模式，都添加image_base64字段
                        if "image_base64" in result:
                            summary["image_base64"] = result["image_base64"]

                        # 在非精简模式下添加详细检测结果
                        if not minimal:
                            summary["detection_results"] = result["detection_results"]

                        # 输出JSON结果
                        output_json(summary)

                frame_count += 1

                # 在持续模式下，等待指定的间隔时间
                if continuous_mode:
                    # 计算需要等待的时间（考虑处理时间）
                    wait_time = max(0, interval - processing_time)
                    if wait_time > 0:
                        time.sleep(wait_time)

        except KeyboardInterrupt:
            log_info("接收到中断信号，停止处理")

        # 非持续模式下，汇总所有结果
        if not continuous_mode:
            # 计算总人数和安全帽数量
            total_person_count = sum(result["person_count"] for result in all_results)
            total_helmet_count = sum(result["helmet_count"] for result in all_results)
            gap_person_and_helmet = total_person_count - total_helmet_count

            # 只保留必要的汇总信息，减少数据量
            summary = {
                "frame_count": frame_count - 1,  # 最后处理的帧号
                "total_frames_processed": frame_count,
                "total_person_count": total_person_count,
                "total_helmet_count": total_helmet_count,
                "gap_person_and_helmet": gap_person_and_helmet
            }

            # 只有在有未佩戴安全帽的情况下才添加详细结果
            if gap_person_and_helmet > 0:
                # 只保留未佩戴安全帽的帧的结果
                unsafe_frames = [r for r in all_results if r["gap_person_and_helmet"] > 0]
                if unsafe_frames and not minimal:
                    # 在非精简模式下添加完整的不安全帧信息
                    summary["unsafe_frames"] = unsafe_frames
                elif unsafe_frames and minimal:
                    # 在精简模式下只添加必要的不安全帧信息
                    minimal_frames = []
                    for frame in unsafe_frames:
                        minimal_frame = {
                            "frame_count": frame.get("frame_count", 0),
                            "person_count": frame.get("person_count", 0),
                            "helmet_count": frame.get("helmet_count", 0),
                            "gap_person_and_helmet": frame.get("gap_person_and_helmet", 0)
                        }
                        # 添加base64编码的图像数据
                        if "image_base64" in frame:
                            minimal_frame["image_base64"] = frame["image_base64"]
                        minimal_frames.append(minimal_frame)
                    summary["unsafe_frames"] = minimal_frames

            log_info(f"总共含有 {total_person_count} 个人")
            log_info(f"总共含有 {total_helmet_count} 个安全帽")
            log_info(f"未佩戴安全帽人数: {gap_person_and_helmet}")

            # 输出最终结果
            output_json(summary)
            return summary

        return {"message": "持续监控模式已完成"}

    except Exception as e:
        error_msg = {"error": f"处理视频流时出错: {str(e)}"}
        output_json(error_msg)
        return error_msg

    finally:
        if 'cap' in locals() and cap is not None:
            cap.release()
        if 'net' in locals() and net is not None:
            net.release_resource()

def main():
    """主函数，处理命令行参数"""
    # 在Linux系统上设置环境变量，确保正确使用GPU
    if platform.system() == "Linux":
        # 设置CUDA相关环境变量
        os.environ["CUDA_VISIBLE_DEVICES"] = "0"  # 使用第一个GPU
        log_info(f"设置CUDA_VISIBLE_DEVICES={os.environ['CUDA_VISIBLE_DEVICES']}")
        
        # 确保Python输出不被缓存
        os.environ["PYTHONUNBUFFERED"] = "1"
        
        # 检查OpenCV是否支持CUDA
        try:
            cv_build_info = cv2.getBuildInformation()
            if "NVIDIA CUDA" in cv_build_info and "YES" in cv_build_info.split("NVIDIA CUDA")[1].split("\n")[0]:
                log_info("OpenCV已启用CUDA支持")
            else:
                log_info("OpenCV未启用CUDA支持，将使用CPU")
        except Exception as e:
            log_info(f"检查OpenCV CUDA支持时出错: {str(e)}")
        
    parser = argparse.ArgumentParser(description="安全帽检测程序")
    parser.add_argument("--rtsp", required=True, help="RTSP视频流地址")
    parser.add_argument("--output", default=None, help="输出目录")
    parser.add_argument("--frames", type=int, default=1, help="非持续模式下处理的帧数限制")
    parser.add_argument("--continuous", action="store_true", help="持续监控模式，每次处理最新的一帧")
    parser.add_argument("--interval", type=int, default=1, help="持续监控模式下的处理间隔时间（秒）")
    parser.add_argument("--debug", action="store_true", help="启用调试模式，输出更多日志")
    parser.add_argument("--minimal", action="store_true", help="启用精简模式，只输出必要的JSON数据")
    parser.add_argument("--redis-key", default=None, help="Redis键，用于创建子目录")
    parser.add_argument("--use-cpu", action="store_true", help="强制使用CPU进行推理，即使CUDA可用")
    args = parser.parse_args()
    
    # 检查ONNX Runtime和GPU支持
    try:
        import onnxruntime as ort
        ort_device = ort.get_device()
        if args.debug:
            log_info(f"ONNX Runtime 设备: {ort_device}")
            log_info(f"可用执行提供程序: {ort.get_available_providers()}")
        
        if ort_device != "GPU" and not args.use_cpu:
            log_info("CUDA加速不可用。如需GPU加速，请安装 onnxruntime-gpu")
    except ImportError:
        log_info("未找到ONNX Runtime，将使用OpenCV DNN")
    except Exception as e:
        log_info(f"检查ONNX Runtime时出错: {str(e)}")

    # 设置调试模式
    global DEBUG_MODE
    DEBUG_MODE = args.debug

    # 处理视频流
    process_rtsp_stream(
        args.rtsp,
        args.output,
        args.frames,
        args.continuous,
        args.interval,
        args.minimal,
        args.redis_key,
        args.use_cpu
    )

    # 在非持续模式下，输出最终结果由process_rtsp_stream函数处理

if __name__ == "__main__":
    main()
