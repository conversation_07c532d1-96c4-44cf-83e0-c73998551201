"""
批量图片处理脚本
接收多张图片进行批量推理，提高GPU利用率
"""
import os
import sys
import time
import numpy as np
import cv2
import base64
import json
import argparse
import platform
from typing import List, Dict, Any

# 添加内置labels列表，包含人和安全帽类别
labels = ["person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light",
         "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow",
         "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
         "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard",
         "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
         "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
         "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone",
         "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear",
         "hair drier", "toothbrush", "helmet"]  # 添加helmet类别

# 是否启用调试日志
DEBUG_MODE = False

def log_info(msg):
    """简单日志函数，只在DEBUG_MODE为True时输出"""
    if DEBUG_MODE:
        print(f"[INFO] {msg}", file=sys.stderr)

def output_json(data):
    """输出JSON数据到标准输出，用于Java程序读取"""
    print(json.dumps(data, cls=NumpyEncoder))
    sys.stdout.flush()  # 确保输出被立即刷新

# 自定义JSON编码器，处理numpy数组
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)

class BatchYOLOV7Processor(object):
    def __init__(self, yolo_model_path, yolo_model_width, yolo_model_height):
        self.yolo_model_path = yolo_model_path
        self.yolo_model = None
        self.yolo_model_width = yolo_model_width
        self.yolo_model_height = yolo_model_height
        # YOLOv7配置
        self.conf_threshold = 0.65
        self.nms_threshold = 0.45

    def init_resource(self):
        """初始化模型资源"""
        try:
            log_info(f"加载YOLO模型: {self.yolo_model_path}")
            self.yolo_model = cv2.dnn.readNetFromONNX(self.yolo_model_path)
            log_info(f"加载YOLO模型成功")
        except Exception as e:
            log_info(f"加载模型失败: {str(e)}")
            raise

    def process_batch(self, images: List[np.ndarray]) -> List[Dict[str, Any]]:
        """批量处理图片"""
        try:
            if not images:
                return []

            batch_size = len(images)
            log_info(f"开始批量处理 {batch_size} 张图片")

            # 预处理所有图片
            batch_blobs = []
            original_shapes = []
            
            for image in images:
                original_shapes.append((image.shape[1], image.shape[0]))  # (width, height)
                resized_image = cv2.resize(image, (self.yolo_model_width, self.yolo_model_height))
                blob = cv2.dnn.blobFromImage(resized_image, 1/255.0, 
                                           (self.yolo_model_width, self.yolo_model_height),
                                           swapRB=True, crop=False)
                batch_blobs.append(blob)

            # 批量推理
            results = []
            for i, blob in enumerate(batch_blobs):
                self.yolo_model.setInput(blob)
                outputs = self.yolo_model.forward(self.yolo_model.getUnconnectedOutLayersNames())
                
                # 处理单张图片的结果
                detection_results = self.process_single_output(outputs, original_shapes[i], images[i])
                results.append(detection_results)

            log_info(f"批量处理完成，处理了 {len(results)} 张图片")
            return results

        except Exception as e:
            log_info(f"批量处理失败: {str(e)}")
            return [{"error": str(e)} for _ in images]

    def process_single_output(self, outputs, original_shape, original_image):
        """处理单张图片的输出"""
        try:
            # 获取输出张量
            outputs = outputs[0]  # 形状应该是 [1, num_boxes, 85]
            
            # 创建列表存储检测结果
            boxes = []
            confidences = []
            class_ids = []
            
            # 解析检测结果
            rows = outputs.shape[1]
            image_width, image_height = original_shape
            x_factor = image_width / self.yolo_model_width
            y_factor = image_height / self.yolo_model_height
            
            for r in range(rows):
                row = outputs[0, r]
                confidence = row[4]
                
                # 只处理置信度高于阈值的检测
                if confidence >= self.conf_threshold:
                    classes_scores = row[5:]
                    class_id = np.argmax(classes_scores)
                    
                    # 如果类别置信度也高于阈值
                    if classes_scores[class_id] >= self.conf_threshold:
                        cx, cy, w, h = row[0], row[1], row[2], row[3]
                        
                        # 转换为图像坐标系中的边界框坐标
                        left = int((cx - w/2) * x_factor)
                        top = int((cy - h/2) * y_factor)
                        width = int(w * x_factor)
                        height = int(h * y_factor)
                        
                        # 添加到检测结果
                        boxes.append([left, top, width, height])
                        confidences.append(float(confidence))
                        class_ids.append(int(class_id))
            
            # 非极大值抑制
            indices = cv2.dnn.NMSBoxes(boxes, confidences, self.conf_threshold, self.nms_threshold)
            
            # 处理检测结果
            detection_results = []
            for i in indices:
                if isinstance(i, list):  # OpenCV 3.x returns list of lists
                    i = i[0]
                box = boxes[i]
                left, top, width, height = box
                class_id = class_ids[i]
                confidence = confidences[i]
                
                # 添加到结果列表
                detection_results.append({
                    "label": labels[class_id],
                    "score": float(confidence),
                    "box": [left, top, left + width, top + height]
                })
            
            # 绘制结果图像
            result_image = self.draw_results_on_image(original_image, detection_results)
            
            # 计算人数和安全帽数量
            person_count = sum(1 for d in detection_results if d["label"] == "person")
            helmet_count = sum(1 for d in detection_results if d["label"] == "helmet")
            gap_person_and_helmet = person_count - helmet_count
            
            # 转换结果图像为base64
            _, buffer = cv2.imencode('.jpg', result_image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return {
                "person_count": person_count,
                "helmet_count": helmet_count,
                "gap_person_and_helmet": gap_person_and_helmet,
                "image_base64": image_base64,
                "detection_results": detection_results if gap_person_and_helmet > 0 else []
            }
            
        except Exception as e:
            log_info(f"处理单张图片输出失败: {str(e)}")
            return {"error": str(e)}

    def draw_results_on_image(self, image, detection_results):
        """在图像上绘制检测结果"""
        try:
            # 复制原始图像
            result_image = image.copy()
            
            # 绘制每个检测框
            for detection in detection_results:
                label = detection["label"]
                score = detection["score"]
                box = detection["box"]
                
                # 绘制边界框
                cv2.rectangle(result_image,
                             (int(box[0]), int(box[1])),
                             (int(box[2]), int(box[3])),
                             (0, 0, 255), 2)
                
                # 绘制标签
                text = f"{label}: {score:.2f}"
                cv2.putText(result_image, text,
                           (int(box[0]), int(box[1]) - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            # 添加人数和安全帽数量信息
            person_count = sum(1 for d in detection_results if d["label"] == "person")
            helmet_count = sum(1 for d in detection_results if d["label"] == "helmet")
            
            cv2.putText(result_image, f"person: {person_count}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(result_image, f"hat: {helmet_count}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            return result_image
        except Exception as e:
            log_info(f"绘制结果失败: {str(e)}")
            return image

    def release_resource(self):
        """释放资源"""
        self.yolo_model = None
        import gc
        gc.collect()
        log_info("资源已释放")

def process_batch_requests(processor: BatchYOLOV7Processor, batch_size: int, timeout_ms: int):
    """处理批量请求的主循环"""
    log_info(f"开始批量处理服务，批大小: {batch_size}, 超时: {timeout_ms}ms")
    
    while True:
        try:
            # 读取输入
            line = sys.stdin.readline()
            if not line:
                break
                
            # 解析请求
            request = json.loads(line.strip())
            images_data = request.get("images", [])
            
            if not images_data:
                output_json({"error": "No images provided"})
                continue
            
            # 解码图片
            images = []
            stream_keys = []
            for img_data in images_data:
                try:
                    image_bytes = base64.b64decode(img_data["image_data"])
                    image_array = np.frombuffer(image_bytes, dtype=np.uint8)
                    image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
                    if image is not None:
                        images.append(image)
                        stream_keys.append(img_data["stream_key"])
                except Exception as e:
                    log_info(f"解码图片失败: {str(e)}")
                    continue
            
            if not images:
                output_json({"error": "No valid images decoded"})
                continue
            
            # 批量处理
            results = processor.process_batch(images)
            
            # 返回结果
            response = {
                "batch_size": len(results),
                "results": results
            }
            output_json(response)
            
        except Exception as e:
            log_info(f"处理请求时出错: {str(e)}")
            output_json({"error": str(e)})

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量安全帽检测程序")
    parser.add_argument("--batch-size", type=int, default=8, help="批处理大小")
    parser.add_argument("--timeout", type=int, default=2000, help="批处理超时时间（毫秒）")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    args = parser.parse_args()
    
    # 设置调试模式
    global DEBUG_MODE
    DEBUG_MODE = args.debug
    
    # 初始化模型
    current_dir = os.path.dirname(os.path.abspath(__file__))
    yolo_model_path = os.path.join(current_dir, "yolov7x.onnx")
    
    if not os.path.exists(yolo_model_path):
        error_msg = {"error": f"YOLO模型文件不存在: {yolo_model_path}"}
        output_json(error_msg)
        return
    
    # 初始化处理器
    yolo_width = 640
    yolo_height = 640
    processor = BatchYOLOV7Processor(yolo_model_path, yolo_width, yolo_height)
    
    try:
        processor.init_resource()
        log_info("批量处理器初始化成功")
        
        # 开始处理请求
        process_batch_requests(processor, args.batch_size, args.timeout)
        
    except Exception as e:
        error_msg = {"error": f"初始化失败: {str(e)}"}
        output_json(error_msg)
    finally:
        processor.release_resource()

if __name__ == "__main__":
    main()
