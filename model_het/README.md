# 安全帽检测模型 - GPU加速版

本目录包含用于安全帽检测的YOLO模型及其Python接口。模型已经优化为支持GPU加速。

## 环境要求

- Python 3.6+
- CUDA 11.0+ (用于GPU加速)
- cuDNN (对应的CUDA版本)

## 安装依赖

```bash
pip install -r requirements.txt
```

## GPU加速说明

模型现在可以使用GPU加速进行推理，提高处理速度。系统会自动检测是否可以使用GPU：

1. 如果检测到CUDA并且安装了onnxruntime-gpu，模型将自动使用GPU加速
2. 如果没有检测到CUDA或者没有安装onnxruntime-gpu，模型将回退到CPU模式
3. 如果需要强制使用CPU (即使有GPU可用)，可以使用`--use-cpu`参数

## 使用方法

在Java应用程序中，通过PythonService调用本模型。该服务已配置为自动利用GPU加速(如果可用)。

## 故障排除

如果遇到GPU相关问题，请尝试以下步骤：

1. 确保安装了正确版本的CUDA和cuDNN
2. 检查是否安装了onnxruntime-gpu：`pip list | grep onnxruntime`
3. 使用`--debug`参数运行程序，查看详细日志
4. 如果仍有问题，可以使用`--use-cpu`参数强制使用CPU模式 