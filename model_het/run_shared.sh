#!/bin/bash

# 设置工作目录
WORK_DIR="/home/<USER>/work/model_het"
cd $WORK_DIR

# 设置日志目录
LOG_DIR="$WORK_DIR/log"
mkdir -p $LOG_DIR

# 设置输出目录
OUTPUT_DIR="/home/<USER>/data/outHet"
mkdir -p $OUTPUT_DIR

# 安装依赖
if [ ! -f "/tmp/model_het_deps_installed" ]; then
    echo "安装依赖..."
    pip install redis numpy opencv-python-headless
    
    # 尝试安装onnxruntime-gpu
    if pip install onnxruntime-gpu; then
        echo "onnxruntime-gpu安装成功"
    else
        echo "onnxruntime-gpu安装失败，尝试安装onnxruntime"
        pip install onnxruntime
    fi
    
    touch /tmp/model_het_deps_installed
fi

# 设置环境变量
export PYTHONPATH="/home/<USER>/work/float_flask:$PYTHONPATH"
export CUDA_VISIBLE_DEVICES=0
export OUTPUT_DIR=$OUTPUT_DIR
export PYTHONUNBUFFERED=1

# 使用conda环境中的Python解释器
PYTHON_PATH="/home/<USER>/work/float_flask/.conda/bin/python"

# 启动Redis服务器（如果尚未运行）
if ! pgrep -x "redis-server" > /dev/null; then
    echo "启动Redis服务器..."
    redis-server --daemonize yes
    sleep 2
fi

# 定义RTSP流列表
declare -a rtsp_streams=(
    "rtsp://admin:gk123456@************:554/Streaming/Channels/101,2"
    "rtsp://admin:gk123456@************:554/Streaming/Channels/101,2"
    "rtsp://admin:gk123456@************:554/Streaming/Channels/101,2"
    # 可以添加更多RTSP流
)

# 清理旧的进程
echo "清理旧的进程..."
pkill -f "imageToResult.py"
sleep 2

# 启动第一个实例（主实例，负责加载模型）
echo "启动主实例..."
$PYTHON_PATH $WORK_DIR/imageToResult.py --rtsp "${rtsp_streams[0]}" --continuous --interval 1 --minimal --redis-key "het_img:1:1" > $LOG_DIR/stream_1.log 2>&1 &
echo "等待主实例加载模型..."
sleep 10  # 给主实例足够的时间加载模型

# 启动其他实例（轻量级实例，共享模型）
for i in $(seq 1 $((${#rtsp_streams[@]}-1))); do
    echo "启动实例 $((i+1))..."
    $PYTHON_PATH $WORK_DIR/imageToResult.py --rtsp "${rtsp_streams[$i]}" --continuous --interval 1 --minimal --redis-key "het_img:$((i+1)):$((i+1))" > $LOG_DIR/stream_$((i+1)).log 2>&1 &
    sleep 1
done

echo "所有实例已启动，运行 'tail -f $LOG_DIR/stream_*.log' 查看日志"

# 等待所有后台进程
wait 