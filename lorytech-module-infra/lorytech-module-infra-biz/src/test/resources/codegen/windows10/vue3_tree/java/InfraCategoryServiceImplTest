package com.nj9394.module.infra.service.demo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import ut.core.test.com.nj9394.framework.BaseDbUnitTest;

import com.nj9394.module.infra.controller.admin.demo.vo.*;
import com.nj9394.module.infra.dal.dataobject.demo.InfraCategoryDO;
import com.nj9394.module.infra.dal.mysql.demo.InfraCategoryMapper;
import pojo.common.com.nj9394.framework.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static enums.com.nj9394.module.infra.ErrorCodeConstants.*;
import static util.core.test.com.nj9394.framework.AssertUtils.*;
import static util.core.test.com.nj9394.framework.RandomUtils.*;
import static date.util.common.com.nj9394.framework.LocalDateTimeUtils.*;
import static object.util.common.com.nj9394.framework.ObjectUtils.*;
import static date.util.common.com.nj9394.framework.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link InfraCategoryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InfraCategoryServiceImpl.class)
public class InfraCategoryServiceImplTest extends BaseDbUnitTest {

    @Resource
    private InfraCategoryServiceImpl categoryService;

    @Resource
    private InfraCategoryMapper categoryMapper;

    @Test
    public void testCreateCategory_success() {
        // 准备参数
        InfraCategorySaveReqVO createReqVO = randomPojo(InfraCategorySaveReqVO.class).setId(null);

        // 调用
        Long categoryId = categoryService.createCategory(createReqVO);
        // 断言
        assertNotNull(categoryId);
        // 校验记录的属性是否正确
        InfraCategoryDO category = categoryMapper.selectById(categoryId);
        assertPojoEquals(createReqVO, category, "id");
    }

    @Test
    public void testUpdateCategory_success() {
        // mock 数据
        InfraCategoryDO dbCategory = randomPojo(InfraCategoryDO.class);
        categoryMapper.insert(dbCategory);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InfraCategorySaveReqVO updateReqVO = randomPojo(InfraCategorySaveReqVO.class, o -> {
            o.setId(dbCategory.getId()); // 设置更新的 ID
        });

        // 调用
        categoryService.updateCategory(updateReqVO);
        // 校验是否更新正确
        InfraCategoryDO category = categoryMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, category);
    }

    @Test
    public void testUpdateCategory_notExists() {
        // 准备参数
        InfraCategorySaveReqVO updateReqVO = randomPojo(InfraCategorySaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> categoryService.updateCategory(updateReqVO), CATEGORY_NOT_EXISTS);
    }

    @Test
    public void testDeleteCategory_success() {
        // mock 数据
        InfraCategoryDO dbCategory = randomPojo(InfraCategoryDO.class);
        categoryMapper.insert(dbCategory);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCategory.getId();

        // 调用
        categoryService.deleteCategory(id);
       // 校验数据不存在了
       assertNull(categoryMapper.selectById(id));
    }

    @Test
    public void testDeleteCategory_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> categoryService.deleteCategory(id), CATEGORY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetCategoryList() {
       // mock 数据
       InfraCategoryDO dbCategory = randomPojo(InfraCategoryDO.class, o -> { // 等会查询到
           o.setName(null);
       });
       categoryMapper.insert(dbCategory);
       // 测试 name 不匹配
       categoryMapper.insert(cloneIgnoreId(dbCategory, o -> o.setName(null)));
       // 准备参数
       InfraCategoryListReqVO reqVO = new InfraCategoryListReqVO();
       reqVO.setName(null);

       // 调用
       List<InfraCategoryDO> list = categoryService.getCategoryList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbCategory, list.get(0));
    }

}