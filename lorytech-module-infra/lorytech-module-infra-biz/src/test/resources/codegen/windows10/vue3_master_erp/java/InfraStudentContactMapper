package com.nj9394.module.infra.dal.mysql.demo;

import java.util.*;

import pojo.common.com.nj9394.framework.PageResult;
import pojo.common.com.nj9394.framework.PageParam;
import query.core.mybatis.com.nj9394.framework.LambdaQueryWrapperX;
import mapper.core.mybatis.com.nj9394.framework.BaseMapperX;
import com.nj9394.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default PageResult<InfraStudentContactDO> selectPage(PageParam reqVO, Long studentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentContactDO>()
            .eq(InfraStudentContactDO::getStudentId, studentId)
            .orderByDesc(InfraStudentContactDO::getId));
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}