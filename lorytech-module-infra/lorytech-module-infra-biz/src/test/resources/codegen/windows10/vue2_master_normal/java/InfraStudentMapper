package com.nj9394.module.infra.dal.mysql.demo;

import java.util.*;

import pojo.common.com.nj9394.framework.PageResult;
import query.core.mybatis.com.nj9394.framework.LambdaQueryWrapperX;
import mapper.core.mybatis.com.nj9394.framework.BaseMapperX;
import com.nj9394.module.infra.dal.dataobject.demo.InfraStudentDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.infra.controller.admin.demo.vo.*;

/**
 * 学生 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentMapper extends BaseMapperX<InfraStudentDO> {

    default PageResult<InfraStudentDO> selectPage(InfraStudentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentDO>()
                .likeIfPresent(InfraStudentDO::getName, reqVO.getName())
                .eqIfPresent(InfraStudentDO::getBirthday, reqVO.getBirthday())
                .eqIfPresent(InfraStudentDO::getSex, reqVO.getSex())
                .eqIfPresent(InfraStudentDO::getEnabled, reqVO.getEnabled())
                .betweenIfPresent(InfraStudentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InfraStudentDO::getId));
    }

}