<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nj9394.boot</groupId>
        <artifactId>lorytech-module-infra</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>lorytech-module-infra-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        infra 模块，主要提供两块能力：
            1. 我们放基础设施的运维与管理，支撑上层的通用与核心业务。 例如说：定时任务的管理、服务器的信息等等
            2. 研发工具，提升研发效率与质量。 例如说：代码生成器、接口文档等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->

        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.nj9394.boot</groupId>
            <artifactId>lorytech-spring-boot-starter-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId> <!-- 文件客户端：解决 ftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId> <!-- 文件客户端：解决 sftp 连接 -->
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId> <!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>

    </dependencies>

</project>
