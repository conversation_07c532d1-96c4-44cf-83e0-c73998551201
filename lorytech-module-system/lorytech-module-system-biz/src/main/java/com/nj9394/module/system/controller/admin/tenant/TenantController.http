### 获取租户编号 /admin-api/system/get-id-by-name
GET {{baseUrl}}/system/tenant/get-id-by-name?name=新余国科

### 创建租户 /admin-api/system/tenant/create
POST {{baseUrl}}/system/tenant/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

{
  "name": "新余国科",
  "contactName": "芋艿",
  "contactMobile": "***********",
  "status": 0,
  "domain": "https://www.nj9394.com",
  "packageId": 110,
  "expireTime": *************,
  "accountCount": 20,
  "username": "admin",
  "password": "123321"
}
