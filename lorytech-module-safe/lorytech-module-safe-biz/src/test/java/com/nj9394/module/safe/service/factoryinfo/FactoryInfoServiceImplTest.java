package com.nj9394.module.safe.service.factoryinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.factoryinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;
import com.nj9394.module.safe.dal.mysql.factoryinfo.FactoryInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link FactoryInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(FactoryInfoServiceImpl.class)
public class FactoryInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private FactoryInfoServiceImpl factoryInfoService;

    @Resource
    private FactoryInfoMapper factoryInfoMapper;

    @Test
    public void testCreateFactoryInfo_success() {
        // 准备参数
        FactoryInfoSaveReqVO createReqVO = randomPojo(FactoryInfoSaveReqVO.class).setId(null);

        // 调用
        Long factoryInfoId = factoryInfoService.createFactoryInfo(createReqVO);
        // 断言
        assertNotNull(factoryInfoId);
        // 校验记录的属性是否正确
        FactoryInfoDO factoryInfo = factoryInfoMapper.selectById(factoryInfoId);
        assertPojoEquals(createReqVO, factoryInfo, "id");
    }

    @Test
    public void testUpdateFactoryInfo_success() {
        // mock 数据
        FactoryInfoDO dbFactoryInfo = randomPojo(FactoryInfoDO.class);
        factoryInfoMapper.insert(dbFactoryInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        FactoryInfoSaveReqVO updateReqVO = randomPojo(FactoryInfoSaveReqVO.class, o -> {
            o.setId(dbFactoryInfo.getId()); // 设置更新的 ID
        });

        // 调用
        factoryInfoService.updateFactoryInfo(updateReqVO);
        // 校验是否更新正确
        FactoryInfoDO factoryInfo = factoryInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, factoryInfo);
    }

    @Test
    public void testUpdateFactoryInfo_notExists() {
        // 准备参数
        FactoryInfoSaveReqVO updateReqVO = randomPojo(FactoryInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> factoryInfoService.updateFactoryInfo(updateReqVO), FACTORY_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteFactoryInfo_success() {
        // mock 数据
        FactoryInfoDO dbFactoryInfo = randomPojo(FactoryInfoDO.class);
        factoryInfoMapper.insert(dbFactoryInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbFactoryInfo.getId();

        // 调用
        factoryInfoService.deleteFactoryInfo(id);
       // 校验数据不存在了
       assertNull(factoryInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteFactoryInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> factoryInfoService.deleteFactoryInfo(id), FACTORY_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetFactoryInfoPage() {
       // mock 数据
       FactoryInfoDO dbFactoryInfo = randomPojo(FactoryInfoDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setPurpose(null);
           o.setCreateTime(null);
       });
       factoryInfoMapper.insert(dbFactoryInfo);
       // 测试 name 不匹配
       factoryInfoMapper.insert(cloneIgnoreId(dbFactoryInfo, o -> o.setName(null)));
       // 测试 usage 不匹配
       factoryInfoMapper.insert(cloneIgnoreId(dbFactoryInfo, o -> o.setPurpose(null)));
       // 测试 createTime 不匹配
       factoryInfoMapper.insert(cloneIgnoreId(dbFactoryInfo, o -> o.setCreateTime(null)));
       // 准备参数
       FactoryInfoPageReqVO reqVO = new FactoryInfoPageReqVO();
       reqVO.setName(null);
       reqVO.setPurpose(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<FactoryInfoDO> pageResult = factoryInfoService.getFactoryInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbFactoryInfo, pageResult.getList().get(0));
    }

}