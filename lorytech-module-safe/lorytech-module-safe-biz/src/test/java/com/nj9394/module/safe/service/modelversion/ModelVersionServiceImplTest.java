package com.nj9394.module.safe.service.modelversion;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.modelversion.vo.*;
import com.nj9394.module.safe.dal.dataobject.modelversion.ModelVersionDO;
import com.nj9394.module.safe.dal.mysql.modelversion.ModelVersionMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link ModelVersionServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ModelVersionServiceImpl.class)
public class ModelVersionServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ModelVersionServiceImpl modelVersionService;

    @Resource
    private ModelVersionMapper modelVersionMapper;

    @Test
    public void testCreateModelVersion_success() {
        // 准备参数
        ModelVersionSaveReqVO createReqVO = randomPojo(ModelVersionSaveReqVO.class).setId(null);

        // 调用
        Long modelVersionId = modelVersionService.createModelVersion(createReqVO);
        // 断言
        assertNotNull(modelVersionId);
        // 校验记录的属性是否正确
        ModelVersionDO modelVersion = modelVersionMapper.selectById(modelVersionId);
        assertPojoEquals(createReqVO, modelVersion, "id");
    }

    @Test
    public void testUpdateModelVersion_success() {
        // mock 数据
        ModelVersionDO dbModelVersion = randomPojo(ModelVersionDO.class);
        modelVersionMapper.insert(dbModelVersion);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ModelVersionSaveReqVO updateReqVO = randomPojo(ModelVersionSaveReqVO.class, o -> {
            o.setId(dbModelVersion.getId()); // 设置更新的 ID
        });

        // 调用
        modelVersionService.updateModelVersion(updateReqVO);
        // 校验是否更新正确
        ModelVersionDO modelVersion = modelVersionMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, modelVersion);
    }

    @Test
    public void testUpdateModelVersion_notExists() {
        // 准备参数
        ModelVersionSaveReqVO updateReqVO = randomPojo(ModelVersionSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> modelVersionService.updateModelVersion(updateReqVO), MODEL_VERSION_NOT_EXISTS);
    }

    @Test
    public void testDeleteModelVersion_success() {
        // mock 数据
        ModelVersionDO dbModelVersion = randomPojo(ModelVersionDO.class);
        modelVersionMapper.insert(dbModelVersion);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbModelVersion.getId();

        // 调用
        modelVersionService.deleteModelVersion(id);
       // 校验数据不存在了
       assertNull(modelVersionMapper.selectById(id));
    }

    @Test
    public void testDeleteModelVersion_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> modelVersionService.deleteModelVersion(id), MODEL_VERSION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetModelVersionPage() {
       // mock 数据
       ModelVersionDO dbModelVersion = randomPojo(ModelVersionDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setVersion(null);
           o.setUploadTime(null);
           o.setCreateTime(null);
       });
       modelVersionMapper.insert(dbModelVersion);
       // 测试 name 不匹配
       modelVersionMapper.insert(cloneIgnoreId(dbModelVersion, o -> o.setName(null)));
       // 测试 version 不匹配
       modelVersionMapper.insert(cloneIgnoreId(dbModelVersion, o -> o.setVersion(null)));
       // 测试 uploadTime 不匹配
       modelVersionMapper.insert(cloneIgnoreId(dbModelVersion, o -> o.setUploadTime(null)));
       // 测试 createTime 不匹配
       modelVersionMapper.insert(cloneIgnoreId(dbModelVersion, o -> o.setCreateTime(null)));
       // 准备参数
       ModelVersionPageReqVO reqVO = new ModelVersionPageReqVO();
       reqVO.setName(null);
       reqVO.setVersion(null);
       reqVO.setUploadTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ModelVersionDO> pageResult = modelVersionService.getModelVersionPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbModelVersion, pageResult.getList().get(0));
    }

}