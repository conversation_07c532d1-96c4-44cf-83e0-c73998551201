package com.nj9394.module.safe.service.statisticalreport;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.statisticalreport.vo.*;
import com.nj9394.module.safe.dal.dataobject.statisticalreport.StatisticalReportDO;
import com.nj9394.module.safe.dal.mysql.statisticalreport.StatisticalReportMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link StatisticalReportServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(StatisticalReportServiceImpl.class)
public class StatisticalReportServiceImplTest extends BaseDbUnitTest {

    @Resource
    private StatisticalReportServiceImpl statisticalReportService;

    @Resource
    private StatisticalReportMapper statisticalReportMapper;

    @Test
    public void testCreateStatisticalReport_success() {
        // 准备参数
        StatisticalReportSaveReqVO createReqVO = randomPojo(StatisticalReportSaveReqVO.class).setId(null);

        // 调用
        Long statisticalReportId = statisticalReportService.createStatisticalReport(createReqVO);
        // 断言
        assertNotNull(statisticalReportId);
        // 校验记录的属性是否正确
        StatisticalReportDO statisticalReport = statisticalReportMapper.selectById(statisticalReportId);
        assertPojoEquals(createReqVO, statisticalReport, "id");
    }

    @Test
    public void testUpdateStatisticalReport_success() {
        // mock 数据
        StatisticalReportDO dbStatisticalReport = randomPojo(StatisticalReportDO.class);
        statisticalReportMapper.insert(dbStatisticalReport);// @Sql: 先插入出一条存在的数据
        // 准备参数
        StatisticalReportSaveReqVO updateReqVO = randomPojo(StatisticalReportSaveReqVO.class, o -> {
            o.setId(dbStatisticalReport.getId()); // 设置更新的 ID
        });

        // 调用
        statisticalReportService.updateStatisticalReport(updateReqVO);
        // 校验是否更新正确
        StatisticalReportDO statisticalReport = statisticalReportMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, statisticalReport);
    }

    @Test
    public void testUpdateStatisticalReport_notExists() {
        // 准备参数
        StatisticalReportSaveReqVO updateReqVO = randomPojo(StatisticalReportSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> statisticalReportService.updateStatisticalReport(updateReqVO), STATISTICAL_REPORT_NOT_EXISTS);
    }

    @Test
    public void testDeleteStatisticalReport_success() {
        // mock 数据
        StatisticalReportDO dbStatisticalReport = randomPojo(StatisticalReportDO.class);
        statisticalReportMapper.insert(dbStatisticalReport);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbStatisticalReport.getId();

        // 调用
        statisticalReportService.deleteStatisticalReport(id);
       // 校验数据不存在了
       assertNull(statisticalReportMapper.selectById(id));
    }

    @Test
    public void testDeleteStatisticalReport_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> statisticalReportService.deleteStatisticalReport(id), STATISTICAL_REPORT_NOT_EXISTS);
    }

//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetStatisticalReportPage() {
//       // mock 数据
//       StatisticalReportDO dbStatisticalReport = randomPojo(StatisticalReportDO.class, o -> { // 等会查询到
//           o.setReportType(null);
//           o.setGenerationTime(null);
//           o.setCreateTime(null);
//       });
//       statisticalReportMapper.insert(dbStatisticalReport);
//       // 测试 reportType 不匹配
//       statisticalReportMapper.insert(cloneIgnoreId(dbStatisticalReport, o -> o.setReportType(null)));
//       // 测试 generationTime 不匹配
//       statisticalReportMapper.insert(cloneIgnoreId(dbStatisticalReport, o -> o.setGenerationTime(null)));
//       // 测试 createTime 不匹配
//       statisticalReportMapper.insert(cloneIgnoreId(dbStatisticalReport, o -> o.setCreateTime(null)));
//       // 准备参数
//       StatisticalReportPageReqVO reqVO = new StatisticalReportPageReqVO();
//       reqVO.setReportType(null);
//       reqVO.setGenerationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<StatisticalReportDO> pageResult = statisticalReportService.getStatisticalReportPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbStatisticalReport, pageResult.getList().get(0));
//    }

}
