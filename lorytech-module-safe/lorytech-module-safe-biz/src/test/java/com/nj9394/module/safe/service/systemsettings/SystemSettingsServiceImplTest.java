package com.nj9394.module.safe.service.systemsettings;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.systemsettings.vo.*;
import com.nj9394.module.safe.dal.dataobject.systemsettings.SystemSettingsDO;
import com.nj9394.module.safe.dal.mysql.systemsettings.SystemSettingsMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link SystemSettingsServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(SystemSettingsServiceImpl.class)
public class SystemSettingsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private SystemSettingsServiceImpl systemSettingsService;

    @Resource
    private SystemSettingsMapper systemSettingsMapper;

    @Test
    public void testCreateSystemSettings_success() {
        // 准备参数
        SystemSettingsSaveReqVO createReqVO = randomPojo(SystemSettingsSaveReqVO.class).setId(null);

        // 调用
        Long systemSettingsId = systemSettingsService.createSystemSettings(createReqVO);
        // 断言
        assertNotNull(systemSettingsId);
        // 校验记录的属性是否正确
        SystemSettingsDO systemSettings = systemSettingsMapper.selectById(systemSettingsId);
        assertPojoEquals(createReqVO, systemSettings, "id");
    }

    @Test
    public void testUpdateSystemSettings_success() {
        // mock 数据
        SystemSettingsDO dbSystemSettings = randomPojo(SystemSettingsDO.class);
        systemSettingsMapper.insert(dbSystemSettings);// @Sql: 先插入出一条存在的数据
        // 准备参数
        SystemSettingsSaveReqVO updateReqVO = randomPojo(SystemSettingsSaveReqVO.class, o -> {
            o.setId(dbSystemSettings.getId()); // 设置更新的 ID
        });

        // 调用
        systemSettingsService.updateSystemSettings(updateReqVO);
        // 校验是否更新正确
        SystemSettingsDO systemSettings = systemSettingsMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, systemSettings);
    }

    @Test
    public void testUpdateSystemSettings_notExists() {
        // 准备参数
        SystemSettingsSaveReqVO updateReqVO = randomPojo(SystemSettingsSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> systemSettingsService.updateSystemSettings(updateReqVO), SYSTEM_SETTINGS_NOT_EXISTS);
    }

    @Test
    public void testDeleteSystemSettings_success() {
        // mock 数据
        SystemSettingsDO dbSystemSettings = randomPojo(SystemSettingsDO.class);
        systemSettingsMapper.insert(dbSystemSettings);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbSystemSettings.getId();

        // 调用
        systemSettingsService.deleteSystemSettings(id);
       // 校验数据不存在了
       assertNull(systemSettingsMapper.selectById(id));
    }

    @Test
    public void testDeleteSystemSettings_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> systemSettingsService.deleteSystemSettings(id), SYSTEM_SETTINGS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSystemSettingsPage() {
       // mock 数据
       SystemSettingsDO dbSystemSettings = randomPojo(SystemSettingsDO.class, o -> { // 等会查询到
           o.setSettingName(null);
           o.setSettingValue(null);
           o.setCreateTime(null);
       });
       systemSettingsMapper.insert(dbSystemSettings);
       // 测试 settingName 不匹配
       systemSettingsMapper.insert(cloneIgnoreId(dbSystemSettings, o -> o.setSettingName(null)));
       // 测试 settingValue 不匹配
       systemSettingsMapper.insert(cloneIgnoreId(dbSystemSettings, o -> o.setSettingValue(null)));
       // 测试 createTime 不匹配
       systemSettingsMapper.insert(cloneIgnoreId(dbSystemSettings, o -> o.setCreateTime(null)));
       // 准备参数
       SystemSettingsPageReqVO reqVO = new SystemSettingsPageReqVO();
       reqVO.setSettingName(null);
       reqVO.setSettingValue(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<SystemSettingsDO> pageResult = systemSettingsService.getSystemSettingsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbSystemSettings, pageResult.getList().get(0));
    }

}