package com.nj9394.module.safe.service.quotaanalysis;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.*;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.dal.mysql.quotaanalysis.QuotaAnalysisMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link QuotaAnalysisServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(QuotaAnalysisServiceImpl.class)
public class QuotaAnalysisServiceImplTest extends BaseDbUnitTest {

    @Resource
    private QuotaAnalysisServiceImpl quotaAnalysisService;

    @Resource
    private QuotaAnalysisMapper quotaAnalysisMapper;

    @Test
    public void testCreateQuotaAnalysis_success() {
        // 准备参数
        QuotaAnalysisSaveReqVO createReqVO = randomPojo(QuotaAnalysisSaveReqVO.class).setId(null);

        // 调用
        Long quotaAnalysisId = quotaAnalysisService.createQuotaAnalysis(createReqVO);
        // 断言
        assertNotNull(quotaAnalysisId);
        // 校验记录的属性是否正确
        QuotaAnalysisDO quotaAnalysis = quotaAnalysisMapper.selectById(quotaAnalysisId);
        assertPojoEquals(createReqVO, quotaAnalysis, "id");
    }

    @Test
    public void testUpdateQuotaAnalysis_success() {
        // mock 数据
        QuotaAnalysisDO dbQuotaAnalysis = randomPojo(QuotaAnalysisDO.class);
        quotaAnalysisMapper.insert(dbQuotaAnalysis);// @Sql: 先插入出一条存在的数据
        // 准备参数
        QuotaAnalysisSaveReqVO updateReqVO = randomPojo(QuotaAnalysisSaveReqVO.class, o -> {
            o.setId(dbQuotaAnalysis.getId()); // 设置更新的 ID
        });

        // 调用
        quotaAnalysisService.updateQuotaAnalysis(updateReqVO);
        // 校验是否更新正确
        QuotaAnalysisDO quotaAnalysis = quotaAnalysisMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, quotaAnalysis);
    }

    @Test
    public void testUpdateQuotaAnalysis_notExists() {
        // 准备参数
        QuotaAnalysisSaveReqVO updateReqVO = randomPojo(QuotaAnalysisSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> quotaAnalysisService.updateQuotaAnalysis(updateReqVO), QUOTA_ANALYSIS_NOT_EXISTS);
    }

    @Test
    public void testDeleteQuotaAnalysis_success() {
        // mock 数据
        QuotaAnalysisDO dbQuotaAnalysis = randomPojo(QuotaAnalysisDO.class);
        quotaAnalysisMapper.insert(dbQuotaAnalysis);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbQuotaAnalysis.getId();

        // 调用
        quotaAnalysisService.deleteQuotaAnalysis(id);
       // 校验数据不存在了
       assertNull(quotaAnalysisMapper.selectById(id));
    }

    @Test
    public void testDeleteQuotaAnalysis_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> quotaAnalysisService.deleteQuotaAnalysis(id), QUOTA_ANALYSIS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetQuotaAnalysisPage() {
       // mock 数据
       QuotaAnalysisDO dbQuotaAnalysis = randomPojo(QuotaAnalysisDO.class, o -> { // 等会查询到
           o.setAreaId(null);
           o.setIsOverQuota(null);
           o.setAlertLevel(null);
           o.setRecordTime(null);
           o.setCreateTime(null);
       });
       quotaAnalysisMapper.insert(dbQuotaAnalysis);
       // 测试 areaId 不匹配
       quotaAnalysisMapper.insert(cloneIgnoreId(dbQuotaAnalysis, o -> o.setAreaId(null)));
       // 测试 isOverQuota 不匹配
       quotaAnalysisMapper.insert(cloneIgnoreId(dbQuotaAnalysis, o -> o.setIsOverQuota(null)));
       // 测试 alertLevel 不匹配
       quotaAnalysisMapper.insert(cloneIgnoreId(dbQuotaAnalysis, o -> o.setAlertLevel(null)));
       // 测试 recordTime 不匹配
       quotaAnalysisMapper.insert(cloneIgnoreId(dbQuotaAnalysis, o -> o.setRecordTime(null)));
       // 测试 createTime 不匹配
       quotaAnalysisMapper.insert(cloneIgnoreId(dbQuotaAnalysis, o -> o.setCreateTime(null)));
       // 准备参数
       QuotaAnalysisPageReqVO reqVO = new QuotaAnalysisPageReqVO();
       reqVO.setAreaId(null);
       reqVO.setIsOverQuota(null);
       reqVO.setAlertLevel(null);
       reqVO.setRecordTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<QuotaAnalysisDO> pageResult = quotaAnalysisService.getQuotaAnalysisPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbQuotaAnalysis, pageResult.getList().get(0));
    }

}