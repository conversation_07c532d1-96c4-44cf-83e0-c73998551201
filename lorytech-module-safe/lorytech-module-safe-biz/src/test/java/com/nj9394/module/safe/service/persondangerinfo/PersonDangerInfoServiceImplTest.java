package com.nj9394.module.safe.service.persondangerinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.persondangerinfo.PersonDangerInfoDO;
import com.nj9394.module.safe.dal.mysql.persondangerinfo.PersonDangerInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link PersonDangerInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(PersonDangerInfoServiceImpl.class)
public class PersonDangerInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PersonDangerInfoServiceImpl personDangerInfoService;

    @Resource
    private PersonDangerInfoMapper personDangerInfoMapper;

    @Test
    public void testCreatePersonDangerInfo_success() {
        // 准备参数
        PersonDangerInfoSaveReqVO createReqVO = randomPojo(PersonDangerInfoSaveReqVO.class).setId(null);

        // 调用
        Long personDangerInfoId = personDangerInfoService.createPersonDangerInfo(createReqVO);
        // 断言
        assertNotNull(personDangerInfoId);
        // 校验记录的属性是否正确
        PersonDangerInfoDO personDangerInfo = personDangerInfoMapper.selectById(personDangerInfoId);
        assertPojoEquals(createReqVO, personDangerInfo, "id");
    }

    @Test
    public void testUpdatePersonDangerInfo_success() {
        // mock 数据
        PersonDangerInfoDO dbPersonDangerInfo = randomPojo(PersonDangerInfoDO.class);
        personDangerInfoMapper.insert(dbPersonDangerInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PersonDangerInfoSaveReqVO updateReqVO = randomPojo(PersonDangerInfoSaveReqVO.class, o -> {
            o.setId(dbPersonDangerInfo.getId()); // 设置更新的 ID
        });

        // 调用
        personDangerInfoService.updatePersonDangerInfo(updateReqVO);
        // 校验是否更新正确
        PersonDangerInfoDO personDangerInfo = personDangerInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, personDangerInfo);
    }

    @Test
    public void testUpdatePersonDangerInfo_notExists() {
        // 准备参数
        PersonDangerInfoSaveReqVO updateReqVO = randomPojo(PersonDangerInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> personDangerInfoService.updatePersonDangerInfo(updateReqVO), PERSON_DANGER_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeletePersonDangerInfo_success() {
        // mock 数据
        PersonDangerInfoDO dbPersonDangerInfo = randomPojo(PersonDangerInfoDO.class);
        personDangerInfoMapper.insert(dbPersonDangerInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPersonDangerInfo.getId();

        // 调用
        personDangerInfoService.deletePersonDangerInfo(id);
       // 校验数据不存在了
       assertNull(personDangerInfoMapper.selectById(id));
    }

    @Test
    public void testDeletePersonDangerInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> personDangerInfoService.deletePersonDangerInfo(id), PERSON_DANGER_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPersonDangerInfoPage() {
       // mock 数据
       PersonDangerInfoDO dbPersonDangerInfo = randomPojo(PersonDangerInfoDO.class, o -> { // 等会查询到
           o.setAreaId(null);
           o.setIsWearingHelmet(null);
           o.setIsWearingWorkClothes(null);
           o.setIsDangerousBehavior(null);
           o.setIsDangerousSituation(null);
           o.setRecordTime(null);
           o.setRecordFile(null);
           o.setCreateTime(null);
       });
       personDangerInfoMapper.insert(dbPersonDangerInfo);
       // 测试 areaId 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setAreaId(null)));
       // 测试 isWearingHelmet 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setIsWearingHelmet(null)));
       // 测试 isWearingWorkClothes 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setIsWearingWorkClothes(null)));
       // 测试 isDangerousBehavior 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setIsDangerousBehavior(null)));
       // 测试 isDangerousSituation 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setIsDangerousSituation(null)));
       // 测试 recordTime 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setRecordTime(null)));
       // 测试 recordFile 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setRecordFile(null)));
       // 测试 createTime 不匹配
       personDangerInfoMapper.insert(cloneIgnoreId(dbPersonDangerInfo, o -> o.setCreateTime(null)));
       // 准备参数
       PersonDangerInfoPageReqVO reqVO = new PersonDangerInfoPageReqVO();
       reqVO.setAreaId(null);
       reqVO.setIsWearingHelmet(null);
       reqVO.setIsWearingWorkClothes(null);
       reqVO.setIsDangerousBehavior(null);
       reqVO.setIsDangerousSituation(null);
       reqVO.setRecordTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setRecordFile(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<PersonDangerInfoDO> pageResult = personDangerInfoService.getPersonDangerInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPersonDangerInfo, pageResult.getList().get(0));
    }

}