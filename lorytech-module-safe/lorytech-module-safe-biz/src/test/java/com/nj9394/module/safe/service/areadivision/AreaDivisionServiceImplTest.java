package com.nj9394.module.safe.service.areadivision;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.areadivision.vo.*;
import com.nj9394.module.safe.dal.dataobject.areadivision.AreaDivisionDO;
import com.nj9394.module.safe.dal.mysql.areadivision.AreaDivisionMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link AreaDivisionServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AreaDivisionServiceImpl.class)
public class AreaDivisionServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AreaDivisionServiceImpl areaDivisionService;

    @Resource
    private AreaDivisionMapper areaDivisionMapper;

    @Test
    public void testCreateAreaDivision_success() {
        // 准备参数
        AreaDivisionSaveReqVO createReqVO = randomPojo(AreaDivisionSaveReqVO.class).setId(null);

        // 调用
        Long areaDivisionId = areaDivisionService.createAreaDivision(createReqVO);
        // 断言
        assertNotNull(areaDivisionId);
        // 校验记录的属性是否正确
        AreaDivisionDO areaDivision = areaDivisionMapper.selectById(areaDivisionId);
        assertPojoEquals(createReqVO, areaDivision, "id");
    }

    @Test
    public void testUpdateAreaDivision_success() {
        // mock 数据
        AreaDivisionDO dbAreaDivision = randomPojo(AreaDivisionDO.class);
        areaDivisionMapper.insert(dbAreaDivision);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AreaDivisionSaveReqVO updateReqVO = randomPojo(AreaDivisionSaveReqVO.class, o -> {
            o.setId(dbAreaDivision.getId()); // 设置更新的 ID
        });

        // 调用
        areaDivisionService.updateAreaDivision(updateReqVO);
        // 校验是否更新正确
        AreaDivisionDO areaDivision = areaDivisionMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, areaDivision);
    }

    @Test
    public void testUpdateAreaDivision_notExists() {
        // 准备参数
        AreaDivisionSaveReqVO updateReqVO = randomPojo(AreaDivisionSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> areaDivisionService.updateAreaDivision(updateReqVO), AREA_DIVISION_NOT_EXISTS);
    }

    @Test
    public void testDeleteAreaDivision_success() {
        // mock 数据
        AreaDivisionDO dbAreaDivision = randomPojo(AreaDivisionDO.class);
        areaDivisionMapper.insert(dbAreaDivision);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAreaDivision.getId();

        // 调用
        areaDivisionService.deleteAreaDivision(id);
       // 校验数据不存在了
       assertNull(areaDivisionMapper.selectById(id));
    }

    @Test
    public void testDeleteAreaDivision_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> areaDivisionService.deleteAreaDivision(id), AREA_DIVISION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetAreaDivisionPage() {
       // mock 数据
       AreaDivisionDO dbAreaDivision = randomPojo(AreaDivisionDO.class, o -> { // 等会查询到
           o.setFactoryId(null);
           o.setName(null);
           o.setDangerLevel(null);
           o.setCreateTime(null);
       });
       areaDivisionMapper.insert(dbAreaDivision);
       // 测试 factoryId 不匹配
       areaDivisionMapper.insert(cloneIgnoreId(dbAreaDivision, o -> o.setFactoryId(null)));
       // 测试 name 不匹配
       areaDivisionMapper.insert(cloneIgnoreId(dbAreaDivision, o -> o.setName(null)));
       // 测试 dangerLevel 不匹配
       areaDivisionMapper.insert(cloneIgnoreId(dbAreaDivision, o -> o.setDangerLevel(null)));
       // 测试 createTime 不匹配
       areaDivisionMapper.insert(cloneIgnoreId(dbAreaDivision, o -> o.setCreateTime(null)));
       // 准备参数
       AreaDivisionPageReqVO reqVO = new AreaDivisionPageReqVO();
       reqVO.setFactoryId(null);
       reqVO.setName(null);
       reqVO.setDangerLevel(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<AreaDivisionDO> pageResult = areaDivisionService.getAreaDivisionPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbAreaDivision, pageResult.getList().get(0));
    }

}