package com.nj9394.module.safe.service.userinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.userinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.userinfo.UserInfoDO;
import com.nj9394.module.safe.dal.mysql.userinfo.UserInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link UserInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(UserInfoServiceImpl.class)
public class UserInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private UserInfoServiceImpl userInfoService;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Test
    public void testCreateUserInfo_success() {
        // 准备参数
        UserInfoSaveReqVO createReqVO = randomPojo(UserInfoSaveReqVO.class).setId(null);

        // 调用
        Long userInfoId = userInfoService.createUserInfo(createReqVO);
        // 断言
        assertNotNull(userInfoId);
        // 校验记录的属性是否正确
        UserInfoDO userInfo = userInfoMapper.selectById(userInfoId);
        assertPojoEquals(createReqVO, userInfo, "id");
    }

    @Test
    public void testUpdateUserInfo_success() {
        // mock 数据
        UserInfoDO dbUserInfo = randomPojo(UserInfoDO.class);
        userInfoMapper.insert(dbUserInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        UserInfoSaveReqVO updateReqVO = randomPojo(UserInfoSaveReqVO.class, o -> {
            o.setId(dbUserInfo.getId()); // 设置更新的 ID
        });

        // 调用
        userInfoService.updateUserInfo(updateReqVO);
        // 校验是否更新正确
        UserInfoDO userInfo = userInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, userInfo);
    }

    @Test
    public void testUpdateUserInfo_notExists() {
        // 准备参数
        UserInfoSaveReqVO updateReqVO = randomPojo(UserInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> userInfoService.updateUserInfo(updateReqVO), USER_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteUserInfo_success() {
        // mock 数据
        UserInfoDO dbUserInfo = randomPojo(UserInfoDO.class);
        userInfoMapper.insert(dbUserInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbUserInfo.getId();

        // 调用
        userInfoService.deleteUserInfo(id);
       // 校验数据不存在了
       assertNull(userInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteUserInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> userInfoService.deleteUserInfo(id), USER_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetUserInfoPage() {
       // mock 数据
       UserInfoDO dbUserInfo = randomPojo(UserInfoDO.class, o -> { // 等会查询到
           o.setUsername(null);
           o.setPassword(null);
           o.setRole(null);
           o.setCreateTime(null);
       });
       userInfoMapper.insert(dbUserInfo);
       // 测试 username 不匹配
       userInfoMapper.insert(cloneIgnoreId(dbUserInfo, o -> o.setUsername(null)));
       // 测试 password 不匹配
       userInfoMapper.insert(cloneIgnoreId(dbUserInfo, o -> o.setPassword(null)));
       // 测试 role 不匹配
       userInfoMapper.insert(cloneIgnoreId(dbUserInfo, o -> o.setRole(null)));
       // 测试 createTime 不匹配
       userInfoMapper.insert(cloneIgnoreId(dbUserInfo, o -> o.setCreateTime(null)));
       // 准备参数
       UserInfoPageReqVO reqVO = new UserInfoPageReqVO();
       reqVO.setUsername(null);
       reqVO.setPassword(null);
       reqVO.setRole(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<UserInfoDO> pageResult = userInfoService.getUserInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbUserInfo, pageResult.getList().get(0));
    }

}