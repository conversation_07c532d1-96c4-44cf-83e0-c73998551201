package com.nj9394.module.safe.service.datastorage;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.datastorage.vo.*;
import com.nj9394.module.safe.dal.dataobject.datastorage.DataStorageDO;
import com.nj9394.module.safe.dal.mysql.datastorage.DataStorageMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link DataStorageServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DataStorageServiceImpl.class)
public class DataStorageServiceImplTest extends BaseDbUnitTest {

    @Resource
    private DataStorageServiceImpl dataStorageService;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Test
    public void testCreateDataStorage_success() {
        // 准备参数
        DataStorageSaveReqVO createReqVO = randomPojo(DataStorageSaveReqVO.class).setId(null);

        // 调用
        Long dataStorageId = dataStorageService.createDataStorage(createReqVO);
        // 断言
        assertNotNull(dataStorageId);
        // 校验记录的属性是否正确
        DataStorageDO dataStorage = dataStorageMapper.selectById(dataStorageId);
        assertPojoEquals(createReqVO, dataStorage, "id");
    }

    @Test
    public void testUpdateDataStorage_success() {
        // mock 数据
        DataStorageDO dbDataStorage = randomPojo(DataStorageDO.class);
        dataStorageMapper.insert(dbDataStorage);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DataStorageSaveReqVO updateReqVO = randomPojo(DataStorageSaveReqVO.class, o -> {
            o.setId(dbDataStorage.getId()); // 设置更新的 ID
        });

        // 调用
        dataStorageService.updateDataStorage(updateReqVO);
        // 校验是否更新正确
        DataStorageDO dataStorage = dataStorageMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, dataStorage);
    }

    @Test
    public void testUpdateDataStorage_notExists() {
        // 准备参数
        DataStorageSaveReqVO updateReqVO = randomPojo(DataStorageSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> dataStorageService.updateDataStorage(updateReqVO), DATA_STORAGE_NOT_EXISTS);
    }

    @Test
    public void testDeleteDataStorage_success() {
        // mock 数据
        DataStorageDO dbDataStorage = randomPojo(DataStorageDO.class);
        dataStorageMapper.insert(dbDataStorage);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDataStorage.getId();

        // 调用
        dataStorageService.deleteDataStorage(id);
       // 校验数据不存在了
       assertNull(dataStorageMapper.selectById(id));
    }

    @Test
    public void testDeleteDataStorage_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> dataStorageService.deleteDataStorage(id), DATA_STORAGE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDataStoragePage() {
       // mock 数据
       DataStorageDO dbDataStorage = randomPojo(DataStorageDO.class, o -> { // 等会查询到
           o.setMonitoringData(null);
           o.setBackupTime(null);
           o.setCreateTime(null);
       });
       dataStorageMapper.insert(dbDataStorage);
       // 测试 monitoringData 不匹配
       dataStorageMapper.insert(cloneIgnoreId(dbDataStorage, o -> o.setMonitoringData(null)));
       // 测试 backupTime 不匹配
       dataStorageMapper.insert(cloneIgnoreId(dbDataStorage, o -> o.setBackupTime(null)));
       // 测试 createTime 不匹配
       dataStorageMapper.insert(cloneIgnoreId(dbDataStorage, o -> o.setCreateTime(null)));
       // 准备参数
       DataStoragePageReqVO reqVO = new DataStoragePageReqVO();
       reqVO.setMonitoringData(null);
       reqVO.setBackupTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<DataStorageDO> pageResult = dataStorageService.getDataStoragePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbDataStorage, pageResult.getList().get(0));
    }

}