package com.nj9394.module.safe.service.videostreaminfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.videostreaminfo.VideoStreamInfoDO;
import com.nj9394.module.safe.dal.mysql.videostreaminfo.VideoStreamInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link VideoStreamInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(VideoStreamInfoServiceImpl.class)
public class VideoStreamInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private VideoStreamInfoServiceImpl videoStreamInfoService;

    @Resource
    private VideoStreamInfoMapper videoStreamInfoMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Test
    public void testCreateVideoStreamInfo_success() {
        // 准备参数
        VideoStreamInfoSaveReqVO createReqVO = randomPojo(VideoStreamInfoSaveReqVO.class).setId(null);

        // 调用
        Long videoStreamInfoId = videoStreamInfoService.createVideoStreamInfo(createReqVO);
        // 断言
        assertNotNull(videoStreamInfoId);
        // 校验记录的属性是否正确
        VideoStreamInfoDO videoStreamInfo = videoStreamInfoMapper.selectById(videoStreamInfoId);
        assertPojoEquals(createReqVO, videoStreamInfo, "id");
    }

    @Test
    public void testUpdateVideoStreamInfo_success() {
        // mock 数据
        VideoStreamInfoDO dbVideoStreamInfo = randomPojo(VideoStreamInfoDO.class);
        videoStreamInfoMapper.insert(dbVideoStreamInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        VideoStreamInfoSaveReqVO updateReqVO = randomPojo(VideoStreamInfoSaveReqVO.class, o -> {
            o.setId(dbVideoStreamInfo.getId()); // 设置更新的 ID
        });

        // 调用
        videoStreamInfoService.updateVideoStreamInfo(updateReqVO);
        // 校验是否更新正确
        VideoStreamInfoDO videoStreamInfo = videoStreamInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, videoStreamInfo);
    }

    @Test
    public void testUpdateVideoStreamInfo_notExists() {
        // 准备参数
        VideoStreamInfoSaveReqVO updateReqVO = randomPojo(VideoStreamInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> videoStreamInfoService.updateVideoStreamInfo(updateReqVO), VIDEO_STREAM_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteVideoStreamInfo_success() {
        // mock 数据
        VideoStreamInfoDO dbVideoStreamInfo = randomPojo(VideoStreamInfoDO.class);
        videoStreamInfoMapper.insert(dbVideoStreamInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbVideoStreamInfo.getId();

        // 调用
        videoStreamInfoService.deleteVideoStreamInfo(id);
       // 校验数据不存在了
       assertNull(videoStreamInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteVideoStreamInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> videoStreamInfoService.deleteVideoStreamInfo(id), VIDEO_STREAM_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetVideoStreamInfoPage() {
       // mock 数据
       VideoStreamInfoDO dbVideoStreamInfo = randomPojo(VideoStreamInfoDO.class, o -> { // 等会查询到
           o.setCameraId(null);
           o.setRtspAddress(null);
           o.setCreateTime(null);
       });
       videoStreamInfoMapper.insert(dbVideoStreamInfo);
       // 测试 cameraId 不匹配
       videoStreamInfoMapper.insert(cloneIgnoreId(dbVideoStreamInfo, o -> o.setCameraId(null)));
       // 测试 rtspAddress 不匹配
       videoStreamInfoMapper.insert(cloneIgnoreId(dbVideoStreamInfo, o -> o.setRtspAddress(null)));
       // 测试 createTime 不匹配
       videoStreamInfoMapper.insert(cloneIgnoreId(dbVideoStreamInfo, o -> o.setCreateTime(null)));
       // 准备参数
       VideoStreamInfoPageReqVO reqVO = new VideoStreamInfoPageReqVO();
       reqVO.setCameraId(null);
       reqVO.setRtspAddress(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<VideoStreamInfoDO> pageResult = videoStreamInfoService.getVideoStreamInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbVideoStreamInfo, pageResult.getList().get(0));
    }

}
