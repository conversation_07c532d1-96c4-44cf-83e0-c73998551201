package com.nj9394.module.safe.service.emergencyplan;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.emergencyplan.vo.*;
import com.nj9394.module.safe.dal.dataobject.emergencyplan.EmergencyPlanDO;
import com.nj9394.module.safe.dal.mysql.emergencyplan.EmergencyPlanMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link EmergencyPlanServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(EmergencyPlanServiceImpl.class)
public class EmergencyPlanServiceImplTest extends BaseDbUnitTest {

    @Resource
    private EmergencyPlanServiceImpl emergencyPlanService;

    @Resource
    private EmergencyPlanMapper emergencyPlanMapper;

    @Test
    public void testCreateEmergencyPlan_success() {
        // 准备参数
        EmergencyPlanSaveReqVO createReqVO = randomPojo(EmergencyPlanSaveReqVO.class).setId(null);

        // 调用
        Long emergencyPlanId = emergencyPlanService.createEmergencyPlan(createReqVO);
        // 断言
        assertNotNull(emergencyPlanId);
        // 校验记录的属性是否正确
        EmergencyPlanDO emergencyPlan = emergencyPlanMapper.selectById(emergencyPlanId);
        assertPojoEquals(createReqVO, emergencyPlan, "id");
    }

    @Test
    public void testUpdateEmergencyPlan_success() {
        // mock 数据
        EmergencyPlanDO dbEmergencyPlan = randomPojo(EmergencyPlanDO.class);
        emergencyPlanMapper.insert(dbEmergencyPlan);// @Sql: 先插入出一条存在的数据
        // 准备参数
        EmergencyPlanSaveReqVO updateReqVO = randomPojo(EmergencyPlanSaveReqVO.class, o -> {
            o.setId(dbEmergencyPlan.getId()); // 设置更新的 ID
        });

        // 调用
        emergencyPlanService.updateEmergencyPlan(updateReqVO);
        // 校验是否更新正确
        EmergencyPlanDO emergencyPlan = emergencyPlanMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, emergencyPlan);
    }

    @Test
    public void testUpdateEmergencyPlan_notExists() {
        // 准备参数
        EmergencyPlanSaveReqVO updateReqVO = randomPojo(EmergencyPlanSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> emergencyPlanService.updateEmergencyPlan(updateReqVO), EMERGENCY_PLAN_NOT_EXISTS);
    }

    @Test
    public void testDeleteEmergencyPlan_success() {
        // mock 数据
        EmergencyPlanDO dbEmergencyPlan = randomPojo(EmergencyPlanDO.class);
        emergencyPlanMapper.insert(dbEmergencyPlan);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbEmergencyPlan.getId();

        // 调用
        emergencyPlanService.deleteEmergencyPlan(id);
       // 校验数据不存在了
       assertNull(emergencyPlanMapper.selectById(id));
    }

    @Test
    public void testDeleteEmergencyPlan_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> emergencyPlanService.deleteEmergencyPlan(id), EMERGENCY_PLAN_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetEmergencyPlanPage() {
       // mock 数据
       EmergencyPlanDO dbEmergencyPlan = randomPojo(EmergencyPlanDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setSteps(null);
           o.setCreateTime(null);
       });
       emergencyPlanMapper.insert(dbEmergencyPlan);
       // 测试 name 不匹配
       emergencyPlanMapper.insert(cloneIgnoreId(dbEmergencyPlan, o -> o.setName(null)));
       // 测试 steps 不匹配
       emergencyPlanMapper.insert(cloneIgnoreId(dbEmergencyPlan, o -> o.setSteps(null)));
       // 测试 createTime 不匹配
       emergencyPlanMapper.insert(cloneIgnoreId(dbEmergencyPlan, o -> o.setCreateTime(null)));
       // 准备参数
       EmergencyPlanPageReqVO reqVO = new EmergencyPlanPageReqVO();
       reqVO.setName(null);
       reqVO.setSteps(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<EmergencyPlanDO> pageResult = emergencyPlanService.getEmergencyPlanPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbEmergencyPlan, pageResult.getList().get(0));
    }

}