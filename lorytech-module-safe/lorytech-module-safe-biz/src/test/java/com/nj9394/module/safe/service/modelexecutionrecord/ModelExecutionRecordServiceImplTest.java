package com.nj9394.module.safe.service.modelexecutionrecord;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo.*;
import com.nj9394.module.safe.dal.dataobject.modelexecutionrecord.ModelExecutionRecordDO;
import com.nj9394.module.safe.dal.mysql.modelexecutionrecord.ModelExecutionRecordMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link ModelExecutionRecordServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ModelExecutionRecordServiceImpl.class)
public class ModelExecutionRecordServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ModelExecutionRecordServiceImpl modelExecutionRecordService;

    @Resource
    private ModelExecutionRecordMapper modelExecutionRecordMapper;

    @Test
    public void testCreateModelExecutionRecord_success() {
        // 准备参数
        ModelExecutionRecordSaveReqVO createReqVO = randomPojo(ModelExecutionRecordSaveReqVO.class).setId(null);

        // 调用
        Long modelExecutionRecordId = modelExecutionRecordService.createModelExecutionRecord(createReqVO);
        // 断言
        assertNotNull(modelExecutionRecordId);
        // 校验记录的属性是否正确
        ModelExecutionRecordDO modelExecutionRecord = modelExecutionRecordMapper.selectById(modelExecutionRecordId);
        assertPojoEquals(createReqVO, modelExecutionRecord, "id");
    }

    @Test
    public void testUpdateModelExecutionRecord_success() {
        // mock 数据
        ModelExecutionRecordDO dbModelExecutionRecord = randomPojo(ModelExecutionRecordDO.class);
        modelExecutionRecordMapper.insert(dbModelExecutionRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ModelExecutionRecordSaveReqVO updateReqVO = randomPojo(ModelExecutionRecordSaveReqVO.class, o -> {
            o.setId(dbModelExecutionRecord.getId()); // 设置更新的 ID
        });

        // 调用
        modelExecutionRecordService.updateModelExecutionRecord(updateReqVO);
        // 校验是否更新正确
        ModelExecutionRecordDO modelExecutionRecord = modelExecutionRecordMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, modelExecutionRecord);
    }

    @Test
    public void testUpdateModelExecutionRecord_notExists() {
        // 准备参数
        ModelExecutionRecordSaveReqVO updateReqVO = randomPojo(ModelExecutionRecordSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> modelExecutionRecordService.updateModelExecutionRecord(updateReqVO), MODEL_EXECUTION_RECORD_NOT_EXISTS);
    }

    @Test
    public void testDeleteModelExecutionRecord_success() {
        // mock 数据
        ModelExecutionRecordDO dbModelExecutionRecord = randomPojo(ModelExecutionRecordDO.class);
        modelExecutionRecordMapper.insert(dbModelExecutionRecord);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbModelExecutionRecord.getId();

        // 调用
        modelExecutionRecordService.deleteModelExecutionRecord(id);
       // 校验数据不存在了
       assertNull(modelExecutionRecordMapper.selectById(id));
    }

    @Test
    public void testDeleteModelExecutionRecord_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> modelExecutionRecordService.deleteModelExecutionRecord(id), MODEL_EXECUTION_RECORD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetModelExecutionRecordPage() {
       // mock 数据
       ModelExecutionRecordDO dbModelExecutionRecord = randomPojo(ModelExecutionRecordDO.class, o -> { // 等会查询到
           o.setModelId(null);
           o.setExecutionTime(null);
           o.setCreateTime(null);
       });
       modelExecutionRecordMapper.insert(dbModelExecutionRecord);
       // 测试 modelId 不匹配
       modelExecutionRecordMapper.insert(cloneIgnoreId(dbModelExecutionRecord, o -> o.setModelId(null)));
       // 测试 executionTime 不匹配
       modelExecutionRecordMapper.insert(cloneIgnoreId(dbModelExecutionRecord, o -> o.setExecutionTime(null)));
       // 测试 createTime 不匹配
       modelExecutionRecordMapper.insert(cloneIgnoreId(dbModelExecutionRecord, o -> o.setCreateTime(null)));
       // 准备参数
       ModelExecutionRecordPageReqVO reqVO = new ModelExecutionRecordPageReqVO();
       reqVO.setModelId(null);
       reqVO.setExecutionTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ModelExecutionRecordDO> pageResult = modelExecutionRecordService.getModelExecutionRecordPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbModelExecutionRecord, pageResult.getList().get(0));
    }

}