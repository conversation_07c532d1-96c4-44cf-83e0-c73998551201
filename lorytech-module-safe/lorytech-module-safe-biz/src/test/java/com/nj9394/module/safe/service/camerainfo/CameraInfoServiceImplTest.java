package com.nj9394.module.safe.service.camerainfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.camerainfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.camerainfo.CameraInfoDO;
import com.nj9394.module.safe.dal.mysql.camerainfo.CameraInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link CameraInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CameraInfoServiceImpl.class)
public class CameraInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CameraInfoServiceImpl cameraInfoService;

    @Resource
    private CameraInfoMapper cameraInfoMapper;

    @Test
    public void testCreateCameraInfo_success() {
        // 准备参数
        CameraInfoSaveReqVO createReqVO = randomPojo(CameraInfoSaveReqVO.class).setId(null);

        // 调用
        Long cameraInfoId = cameraInfoService.createCameraInfo(createReqVO);
        // 断言
        assertNotNull(cameraInfoId);
        // 校验记录的属性是否正确
        CameraInfoDO cameraInfo = cameraInfoMapper.selectById(cameraInfoId);
        assertPojoEquals(createReqVO, cameraInfo, "id");
    }

    @Test
    public void testUpdateCameraInfo_success() {
        // mock 数据
        CameraInfoDO dbCameraInfo = randomPojo(CameraInfoDO.class);
        cameraInfoMapper.insert(dbCameraInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CameraInfoSaveReqVO updateReqVO = randomPojo(CameraInfoSaveReqVO.class, o -> {
            o.setId(dbCameraInfo.getId()); // 设置更新的 ID
        });

        // 调用
        cameraInfoService.updateCameraInfo(updateReqVO);
        // 校验是否更新正确
        CameraInfoDO cameraInfo = cameraInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, cameraInfo);
    }

    @Test
    public void testUpdateCameraInfo_notExists() {
        // 准备参数
        CameraInfoSaveReqVO updateReqVO = randomPojo(CameraInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> cameraInfoService.updateCameraInfo(updateReqVO), CAMERA_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteCameraInfo_success() {
        // mock 数据
        CameraInfoDO dbCameraInfo = randomPojo(CameraInfoDO.class);
        cameraInfoMapper.insert(dbCameraInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCameraInfo.getId();

        // 调用
        cameraInfoService.deleteCameraInfo(id);
       // 校验数据不存在了
       assertNull(cameraInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteCameraInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> cameraInfoService.deleteCameraInfo(id), CAMERA_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetCameraInfoPage() {
       // mock 数据
       CameraInfoDO dbCameraInfo = randomPojo(CameraInfoDO.class, o -> { // 等会查询到
           o.setFactoryId(null);
           o.setNumber(null);
           o.setType(null);
           o.setStatus(null);
           o.setCreateTime(null);
       });
       cameraInfoMapper.insert(dbCameraInfo);
       // 测试 factoryId 不匹配
       cameraInfoMapper.insert(cloneIgnoreId(dbCameraInfo, o -> o.setFactoryId(null)));
       // 测试 number 不匹配
       cameraInfoMapper.insert(cloneIgnoreId(dbCameraInfo, o -> o.setNumber(null)));
       // 测试 type 不匹配
       cameraInfoMapper.insert(cloneIgnoreId(dbCameraInfo, o -> o.setType(null)));
       // 测试 status 不匹配
       cameraInfoMapper.insert(cloneIgnoreId(dbCameraInfo, o -> o.setStatus(null)));
       // 测试 createTime 不匹配
       cameraInfoMapper.insert(cloneIgnoreId(dbCameraInfo, o -> o.setCreateTime(null)));
       // 准备参数
       CameraInfoPageReqVO reqVO = new CameraInfoPageReqVO();
       reqVO.setFactoryId(null);
       reqVO.setNumber(null);
       reqVO.setType(null);
       reqVO.setStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<CameraInfoDO> pageResult = cameraInfoService.getCameraInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbCameraInfo, pageResult.getList().get(0));
    }

}