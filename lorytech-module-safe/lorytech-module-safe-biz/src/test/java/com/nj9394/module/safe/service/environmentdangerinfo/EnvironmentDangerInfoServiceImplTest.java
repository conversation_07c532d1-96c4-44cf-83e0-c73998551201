package com.nj9394.module.safe.service.environmentdangerinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.environmentdangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.environmentdangerinfo.EnvironmentDangerInfoDO;
import com.nj9394.module.safe.dal.mysql.environmentdangerinfo.EnvironmentDangerInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link EnvironmentDangerInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(EnvironmentDangerInfoServiceImpl.class)
public class EnvironmentDangerInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private EnvironmentDangerInfoServiceImpl environmentDangerInfoService;

    @Resource
    private EnvironmentDangerInfoMapper environmentDangerInfoMapper;

    @Test
    public void testCreateEnvironmentDangerInfo_success() {
        // 准备参数
        EnvironmentDangerInfoSaveReqVO createReqVO = randomPojo(EnvironmentDangerInfoSaveReqVO.class).setId(null);

        // 调用
        Long environmentDangerInfoId = environmentDangerInfoService.createEnvironmentDangerInfo(createReqVO);
        // 断言
        assertNotNull(environmentDangerInfoId);
        // 校验记录的属性是否正确
        EnvironmentDangerInfoDO environmentDangerInfo = environmentDangerInfoMapper.selectById(environmentDangerInfoId);
        assertPojoEquals(createReqVO, environmentDangerInfo, "id");
    }

    @Test
    public void testUpdateEnvironmentDangerInfo_success() {
        // mock 数据
        EnvironmentDangerInfoDO dbEnvironmentDangerInfo = randomPojo(EnvironmentDangerInfoDO.class);
        environmentDangerInfoMapper.insert(dbEnvironmentDangerInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        EnvironmentDangerInfoSaveReqVO updateReqVO = randomPojo(EnvironmentDangerInfoSaveReqVO.class, o -> {
            o.setId(dbEnvironmentDangerInfo.getId()); // 设置更新的 ID
        });

        // 调用
        environmentDangerInfoService.updateEnvironmentDangerInfo(updateReqVO);
        // 校验是否更新正确
        EnvironmentDangerInfoDO environmentDangerInfo = environmentDangerInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, environmentDangerInfo);
    }

    @Test
    public void testUpdateEnvironmentDangerInfo_notExists() {
        // 准备参数
        EnvironmentDangerInfoSaveReqVO updateReqVO = randomPojo(EnvironmentDangerInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> environmentDangerInfoService.updateEnvironmentDangerInfo(updateReqVO), ENVIRONMENT_DANGER_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteEnvironmentDangerInfo_success() {
        // mock 数据
        EnvironmentDangerInfoDO dbEnvironmentDangerInfo = randomPojo(EnvironmentDangerInfoDO.class);
        environmentDangerInfoMapper.insert(dbEnvironmentDangerInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbEnvironmentDangerInfo.getId();

        // 调用
        environmentDangerInfoService.deleteEnvironmentDangerInfo(id);
       // 校验数据不存在了
       assertNull(environmentDangerInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteEnvironmentDangerInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> environmentDangerInfoService.deleteEnvironmentDangerInfo(id), ENVIRONMENT_DANGER_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetEnvironmentDangerInfoPage() {
       // mock 数据
       EnvironmentDangerInfoDO dbEnvironmentDangerInfo = randomPojo(EnvironmentDangerInfoDO.class, o -> { // 等会查询到
           o.setAreaId(null);
           o.setIsFire(null);
           o.setIsExplosion(null);
           o.setRecordTime(null);
           o.setCreateTime(null);
       });
       environmentDangerInfoMapper.insert(dbEnvironmentDangerInfo);
       // 测试 areaId 不匹配
       environmentDangerInfoMapper.insert(cloneIgnoreId(dbEnvironmentDangerInfo, o -> o.setAreaId(null)));
       // 测试 isFire 不匹配
       environmentDangerInfoMapper.insert(cloneIgnoreId(dbEnvironmentDangerInfo, o -> o.setIsFire(null)));
       // 测试 isExplosion 不匹配
       environmentDangerInfoMapper.insert(cloneIgnoreId(dbEnvironmentDangerInfo, o -> o.setIsExplosion(null)));
       // 测试 recordTime 不匹配
       environmentDangerInfoMapper.insert(cloneIgnoreId(dbEnvironmentDangerInfo, o -> o.setRecordTime(null)));
       // 测试 createTime 不匹配
       environmentDangerInfoMapper.insert(cloneIgnoreId(dbEnvironmentDangerInfo, o -> o.setCreateTime(null)));
       // 准备参数
       EnvironmentDangerInfoPageReqVO reqVO = new EnvironmentDangerInfoPageReqVO();
       reqVO.setAreaId(null);
       reqVO.setIsFire(null);
       reqVO.setIsExplosion(null);
       reqVO.setRecordTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<EnvironmentDangerInfoDO> pageResult = environmentDangerInfoService.getEnvironmentDangerInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbEnvironmentDangerInfo, pageResult.getList().get(0));
    }

}