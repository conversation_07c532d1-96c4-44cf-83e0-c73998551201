package com.nj9394.module.safe.service.alarminfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.alarminfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.alarminfo.AlarmInfoDO;
import com.nj9394.module.safe.dal.mysql.alarminfo.AlarmInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link AlarmInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AlarmInfoServiceImpl.class)
public class AlarmInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AlarmInfoServiceImpl alarmInfoService;

    @Resource
    private AlarmInfoMapper alarmInfoMapper;

    @Test
    public void testCreateAlarmInfo_success() {
        // 准备参数
        AlarmInfoSaveReqVO createReqVO = randomPojo(AlarmInfoSaveReqVO.class).setId(null);

        // 调用
        Long alarmInfoId = alarmInfoService.createAlarmInfo(createReqVO);
        // 断言
        assertNotNull(alarmInfoId);
        // 校验记录的属性是否正确
        AlarmInfoDO alarmInfo = alarmInfoMapper.selectById(alarmInfoId);
        assertPojoEquals(createReqVO, alarmInfo, "id");
    }

    @Test
    public void testUpdateAlarmInfo_success() {
        // mock 数据
        AlarmInfoDO dbAlarmInfo = randomPojo(AlarmInfoDO.class);
        alarmInfoMapper.insert(dbAlarmInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AlarmInfoSaveReqVO updateReqVO = randomPojo(AlarmInfoSaveReqVO.class, o -> {
            o.setId(dbAlarmInfo.getId()); // 设置更新的 ID
        });

        // 调用
        alarmInfoService.updateAlarmInfo(updateReqVO);
        // 校验是否更新正确
        AlarmInfoDO alarmInfo = alarmInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, alarmInfo);
    }

    @Test
    public void testUpdateAlarmInfo_notExists() {
        // 准备参数
        AlarmInfoSaveReqVO updateReqVO = randomPojo(AlarmInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> alarmInfoService.updateAlarmInfo(updateReqVO), ALARM_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteAlarmInfo_success() {
        // mock 数据
        AlarmInfoDO dbAlarmInfo = randomPojo(AlarmInfoDO.class);
        alarmInfoMapper.insert(dbAlarmInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAlarmInfo.getId();

        // 调用
        alarmInfoService.deleteAlarmInfo(id);
       // 校验数据不存在了
       assertNull(alarmInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteAlarmInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> alarmInfoService.deleteAlarmInfo(id), ALARM_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetAlarmInfoPage() {
       // mock 数据
       AlarmInfoDO dbAlarmInfo = randomPojo(AlarmInfoDO.class, o -> { // 等会查询到
           o.setAreaId(null);
           o.setAlarmType(null);
           o.setAlarmTime(null);
           o.setConfirmedStatus(null);
           o.setCreateTime(null);
       });
       alarmInfoMapper.insert(dbAlarmInfo);
       // 测试 areaId 不匹配
       alarmInfoMapper.insert(cloneIgnoreId(dbAlarmInfo, o -> o.setAreaId(null)));
       // 测试 alarmType 不匹配
       alarmInfoMapper.insert(cloneIgnoreId(dbAlarmInfo, o -> o.setAlarmType(null)));
       // 测试 alarmTime 不匹配
       alarmInfoMapper.insert(cloneIgnoreId(dbAlarmInfo, o -> o.setAlarmTime(null)));
       // 测试 confirmedStatus 不匹配
       alarmInfoMapper.insert(cloneIgnoreId(dbAlarmInfo, o -> o.setConfirmedStatus(null)));
       // 测试 createTime 不匹配
       alarmInfoMapper.insert(cloneIgnoreId(dbAlarmInfo, o -> o.setCreateTime(null)));
       // 准备参数
       AlarmInfoPageReqVO reqVO = new AlarmInfoPageReqVO();
       reqVO.setAreaId(null);
       reqVO.setAlarmType(null);
       reqVO.setAlarmTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setConfirmedStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<AlarmInfoDO> pageResult = alarmInfoService.getAlarmInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbAlarmInfo, pageResult.getList().get(0));
    }

}