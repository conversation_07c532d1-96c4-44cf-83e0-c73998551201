package com.nj9394.module.safe.service.historicaldataquery;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.historicaldataquery.vo.*;
import com.nj9394.module.safe.dal.dataobject.historicaldataquery.HistoricalDataQueryDO;
import com.nj9394.module.safe.dal.mysql.historicaldataquery.HistoricalDataQueryMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link HistoricalDataQueryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(HistoricalDataQueryServiceImpl.class)
public class HistoricalDataQueryServiceImplTest extends BaseDbUnitTest {

    @Resource
    private HistoricalDataQueryServiceImpl historicalDataQueryService;

    @Resource
    private HistoricalDataQueryMapper historicalDataQueryMapper;

    @Test
    public void testCreateHistoricalDataQuery_success() {
        // 准备参数
        HistoricalDataQuerySaveReqVO createReqVO = randomPojo(HistoricalDataQuerySaveReqVO.class).setId(null);

        // 调用
        Long historicalDataQueryId = historicalDataQueryService.createHistoricalDataQuery(createReqVO);
        // 断言
        assertNotNull(historicalDataQueryId);
        // 校验记录的属性是否正确
        HistoricalDataQueryDO historicalDataQuery = historicalDataQueryMapper.selectById(historicalDataQueryId);
        assertPojoEquals(createReqVO, historicalDataQuery, "id");
    }

    @Test
    public void testUpdateHistoricalDataQuery_success() {
        // mock 数据
        HistoricalDataQueryDO dbHistoricalDataQuery = randomPojo(HistoricalDataQueryDO.class);
        historicalDataQueryMapper.insert(dbHistoricalDataQuery);// @Sql: 先插入出一条存在的数据
        // 准备参数
        HistoricalDataQuerySaveReqVO updateReqVO = randomPojo(HistoricalDataQuerySaveReqVO.class, o -> {
            o.setId(dbHistoricalDataQuery.getId()); // 设置更新的 ID
        });

        // 调用
        historicalDataQueryService.updateHistoricalDataQuery(updateReqVO);
        // 校验是否更新正确
        HistoricalDataQueryDO historicalDataQuery = historicalDataQueryMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, historicalDataQuery);
    }

    @Test
    public void testUpdateHistoricalDataQuery_notExists() {
        // 准备参数
        HistoricalDataQuerySaveReqVO updateReqVO = randomPojo(HistoricalDataQuerySaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> historicalDataQueryService.updateHistoricalDataQuery(updateReqVO), HISTORICAL_DATA_QUERY_NOT_EXISTS);
    }

    @Test
    public void testDeleteHistoricalDataQuery_success() {
        // mock 数据
        HistoricalDataQueryDO dbHistoricalDataQuery = randomPojo(HistoricalDataQueryDO.class);
        historicalDataQueryMapper.insert(dbHistoricalDataQuery);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbHistoricalDataQuery.getId();

        // 调用
        historicalDataQueryService.deleteHistoricalDataQuery(id);
       // 校验数据不存在了
       assertNull(historicalDataQueryMapper.selectById(id));
    }

    @Test
    public void testDeleteHistoricalDataQuery_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> historicalDataQueryService.deleteHistoricalDataQuery(id), HISTORICAL_DATA_QUERY_NOT_EXISTS);
    }

//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetHistoricalDataQueryPage() {
//       // mock 数据
//       HistoricalDataQueryDO dbHistoricalDataQuery = randomPojo(HistoricalDataQueryDO.class, o -> { // 等会查询到
//           o.setQueryTime(null);
//           o.setQueryContent(null);
//           o.setResult(null);
//           o.setCreateTime(null);
//       });
//       historicalDataQueryMapper.insert(dbHistoricalDataQuery);
//       // 测试 queryTime 不匹配
//       historicalDataQueryMapper.insert(cloneIgnoreId(dbHistoricalDataQuery, o -> o.setQueryTime(null)));
//       // 测试 queryContent 不匹配
//       historicalDataQueryMapper.insert(cloneIgnoreId(dbHistoricalDataQuery, o -> o.setQueryContent(null)));
//       // 测试 result 不匹配
//       historicalDataQueryMapper.insert(cloneIgnoreId(dbHistoricalDataQuery, o -> o.setResult(null)));
//       // 测试 createTime 不匹配
//       historicalDataQueryMapper.insert(cloneIgnoreId(dbHistoricalDataQuery, o -> o.setCreateTime(null)));
//       // 准备参数
//       HistoricalDataQueryPageReqVO reqVO = new HistoricalDataQueryPageReqVO();
//       reqVO.setQueryTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setQueryContent(null);
//       reqVO.setResult(null);
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<HistoricalDataQueryDO> pageResult = historicalDataQueryService.getHistoricalDataQueryPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbHistoricalDataQuery, pageResult.getList().get(0));
//    }

}
