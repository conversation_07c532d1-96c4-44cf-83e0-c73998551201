package com.nj9394.module.safe.service.areavideostream;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.areavideostream.vo.*;
import com.nj9394.module.safe.dal.dataobject.areavideostream.AreaVideoStreamDO;
import com.nj9394.module.safe.dal.mysql.areavideostream.AreaVideoStreamMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link AreaVideoStreamServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AreaVideoStreamServiceImpl.class)
public class AreaVideoStreamServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AreaVideoStreamServiceImpl areaVideoStreamService;

    @Resource
    private AreaVideoStreamMapper areaVideoStreamMapper;

    @Test
    public void testCreateAreaVideoStream_success() {
        // 准备参数
        AreaVideoStreamSaveReqVO createReqVO = randomPojo(AreaVideoStreamSaveReqVO.class).setId(null);

        // 调用
        Long areaVideoStreamId = areaVideoStreamService.createAreaVideoStream(createReqVO);
        // 断言
        assertNotNull(areaVideoStreamId);
        // 校验记录的属性是否正确
        AreaVideoStreamDO areaVideoStream = areaVideoStreamMapper.selectById(areaVideoStreamId);
        assertPojoEquals(createReqVO, areaVideoStream, "id");
    }

    @Test
    public void testUpdateAreaVideoStream_success() {
        // mock 数据
        AreaVideoStreamDO dbAreaVideoStream = randomPojo(AreaVideoStreamDO.class);
        areaVideoStreamMapper.insert(dbAreaVideoStream);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AreaVideoStreamSaveReqVO updateReqVO = randomPojo(AreaVideoStreamSaveReqVO.class, o -> {
            o.setId(dbAreaVideoStream.getId()); // 设置更新的 ID
        });

        // 调用
        areaVideoStreamService.updateAreaVideoStream(updateReqVO);
        // 校验是否更新正确
        AreaVideoStreamDO areaVideoStream = areaVideoStreamMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, areaVideoStream);
    }

    @Test
    public void testUpdateAreaVideoStream_notExists() {
        // 准备参数
        AreaVideoStreamSaveReqVO updateReqVO = randomPojo(AreaVideoStreamSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> areaVideoStreamService.updateAreaVideoStream(updateReqVO), AREA_VIDEO_STREAM_NOT_EXISTS);
    }

    @Test
    public void testDeleteAreaVideoStream_success() {
        // mock 数据
        AreaVideoStreamDO dbAreaVideoStream = randomPojo(AreaVideoStreamDO.class);
        areaVideoStreamMapper.insert(dbAreaVideoStream);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAreaVideoStream.getId();

        // 调用
        areaVideoStreamService.deleteAreaVideoStream(id);
       // 校验数据不存在了
       assertNull(areaVideoStreamMapper.selectById(id));
    }

    @Test
    public void testDeleteAreaVideoStream_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> areaVideoStreamService.deleteAreaVideoStream(id), AREA_VIDEO_STREAM_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetAreaVideoStreamPage() {
       // mock 数据
       AreaVideoStreamDO dbAreaVideoStream = randomPojo(AreaVideoStreamDO.class, o -> { // 等会查询到
           o.setAreaId(null);
           o.setVideoId(null);
           o.setCreateTime(null);
       });
       areaVideoStreamMapper.insert(dbAreaVideoStream);
       // 测试 areaId 不匹配
       areaVideoStreamMapper.insert(cloneIgnoreId(dbAreaVideoStream, o -> o.setAreaId(null)));
       // 测试 videoId 不匹配
       areaVideoStreamMapper.insert(cloneIgnoreId(dbAreaVideoStream, o -> o.setVideoId(null)));
       // 测试 createTime 不匹配
       areaVideoStreamMapper.insert(cloneIgnoreId(dbAreaVideoStream, o -> o.setCreateTime(null)));
       // 准备参数
       AreaVideoStreamPageReqVO reqVO = new AreaVideoStreamPageReqVO();
       reqVO.setAreaId(null);
       reqVO.setVideoId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<AreaVideoStreamDO> pageResult = areaVideoStreamService.getAreaVideoStreamPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbAreaVideoStream, pageResult.getList().get(0));
    }

}