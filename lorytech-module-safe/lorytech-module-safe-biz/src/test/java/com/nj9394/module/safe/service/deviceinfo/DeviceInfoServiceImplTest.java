package com.nj9394.module.safe.service.deviceinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.nj9394.framework.test.core.ut.BaseDbUnitTest;

import com.nj9394.module.safe.controller.admin.deviceinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.deviceinfo.DeviceInfoDO;
import com.nj9394.module.safe.dal.mysql.deviceinfo.DeviceInfoMapper;
import com.nj9394.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;
import static com.nj9394.framework.test.core.util.AssertUtils.*;
import static com.nj9394.framework.test.core.util.RandomUtils.*;
import static com.nj9394.framework.common.util.date.LocalDateTimeUtils.*;
import static com.nj9394.framework.common.util.object.ObjectUtils.*;
import static com.nj9394.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link DeviceInfoServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DeviceInfoServiceImpl.class)
public class DeviceInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private DeviceInfoServiceImpl deviceInfoService;

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Test
    public void testCreateDeviceInfo_success() {
        // 准备参数
        DeviceInfoSaveReqVO createReqVO = randomPojo(DeviceInfoSaveReqVO.class).setId(null);

        // 调用
        Long deviceInfoId = deviceInfoService.createDeviceInfo(createReqVO);
        // 断言
        assertNotNull(deviceInfoId);
        // 校验记录的属性是否正确
        DeviceInfoDO deviceInfo = deviceInfoMapper.selectById(deviceInfoId);
        assertPojoEquals(createReqVO, deviceInfo, "id");
    }

    @Test
    public void testUpdateDeviceInfo_success() {
        // mock 数据
        DeviceInfoDO dbDeviceInfo = randomPojo(DeviceInfoDO.class);
        deviceInfoMapper.insert(dbDeviceInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DeviceInfoSaveReqVO updateReqVO = randomPojo(DeviceInfoSaveReqVO.class, o -> {
            o.setId(dbDeviceInfo.getId()); // 设置更新的 ID
        });

        // 调用
        deviceInfoService.updateDeviceInfo(updateReqVO);
        // 校验是否更新正确
        DeviceInfoDO deviceInfo = deviceInfoMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, deviceInfo);
    }

    @Test
    public void testUpdateDeviceInfo_notExists() {
        // 准备参数
        DeviceInfoSaveReqVO updateReqVO = randomPojo(DeviceInfoSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> deviceInfoService.updateDeviceInfo(updateReqVO), DEVICE_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteDeviceInfo_success() {
        // mock 数据
        DeviceInfoDO dbDeviceInfo = randomPojo(DeviceInfoDO.class);
        deviceInfoMapper.insert(dbDeviceInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDeviceInfo.getId();

        // 调用
        deviceInfoService.deleteDeviceInfo(id);
       // 校验数据不存在了
       assertNull(deviceInfoMapper.selectById(id));
    }

    @Test
    public void testDeleteDeviceInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> deviceInfoService.deleteDeviceInfo(id), DEVICE_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDeviceInfoPage() {
       // mock 数据
       DeviceInfoDO dbDeviceInfo = randomPojo(DeviceInfoDO.class, o -> { // 等会查询到
           o.setDeviceType(null);
           o.setBrand(null);
           o.setModel(null);
           o.setInstallationLocation(null);
           o.setCreateTime(null);
       });
       deviceInfoMapper.insert(dbDeviceInfo);
       // 测试 deviceType 不匹配
       deviceInfoMapper.insert(cloneIgnoreId(dbDeviceInfo, o -> o.setDeviceType(null)));
       // 测试 brand 不匹配
       deviceInfoMapper.insert(cloneIgnoreId(dbDeviceInfo, o -> o.setBrand(null)));
       // 测试 model 不匹配
       deviceInfoMapper.insert(cloneIgnoreId(dbDeviceInfo, o -> o.setModel(null)));
       // 测试 installationLocation 不匹配
       deviceInfoMapper.insert(cloneIgnoreId(dbDeviceInfo, o -> o.setInstallationLocation(null)));
       // 测试 createTime 不匹配
       deviceInfoMapper.insert(cloneIgnoreId(dbDeviceInfo, o -> o.setCreateTime(null)));
       // 准备参数
       DeviceInfoPageReqVO reqVO = new DeviceInfoPageReqVO();
       reqVO.setDeviceType(null);
       reqVO.setBrand(null);
       reqVO.setModel(null);
       reqVO.setInstallationLocation(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<DeviceInfoDO> pageResult = deviceInfoService.getDeviceInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbDeviceInfo, pageResult.getList().get(0));
    }

}