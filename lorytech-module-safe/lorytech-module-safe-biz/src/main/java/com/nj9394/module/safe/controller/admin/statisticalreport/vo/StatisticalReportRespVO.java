package com.nj9394.module.safe.controller.admin.statisticalreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 统计报表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalReportRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2082")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "报表类型", example = "2")
    @ExcelProperty(value = "报表类型", converter = DictConvert.class)
    @DictFormat("safe_report_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String reportType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "报表标题")
    @ExcelProperty("报表标题")
    private String reportTitle;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime reportStime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime reportEtime;

    @Schema(description = "报表文件")
    @ExcelProperty("报表文件")
    private String reportFile;

}