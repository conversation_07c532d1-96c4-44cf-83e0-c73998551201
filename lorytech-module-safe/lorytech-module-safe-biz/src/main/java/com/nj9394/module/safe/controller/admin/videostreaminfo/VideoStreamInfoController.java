package com.nj9394.module.safe.controller.admin.videostreaminfo;

import cn.hutool.core.collection.CollectionUtil;
import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.framework.excel.core.util.ExcelUtils;
import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoRespVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoRespVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoSaveReqVO;
import com.nj9394.module.safe.dal.dataobject.camerainfo.CameraInfoDO;
import com.nj9394.module.safe.dal.dataobject.videostreaminfo.VideoStreamInfoDO;
import com.nj9394.module.safe.service.camerainfo.CameraInfoService;
import com.nj9394.module.safe.service.videostreaminfo.VideoStreamInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nj9394.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 视频流信息")
@RestController
@RequestMapping("/safe/video-stream-info")
@Validated
public class VideoStreamInfoController {

    @Resource
    private VideoStreamInfoService videoStreamInfoService;

    @Resource
    private CameraInfoService cameraInfoService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @PostMapping("/create")
    @Operation(summary = "创建视频流信息")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:create')")
    public CommonResult<Long> createVideoStreamInfo(@Valid @RequestBody VideoStreamInfoSaveReqVO createReqVO) {
        return success(videoStreamInfoService.createVideoStreamInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新视频流信息")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:update')")
    public CommonResult<Boolean> updateVideoStreamInfo(@Valid @RequestBody VideoStreamInfoSaveReqVO updateReqVO) {
        videoStreamInfoService.updateVideoStreamInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除视频流信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:delete')")
    public CommonResult<Boolean> deleteVideoStreamInfo(@RequestParam("id") Long id) {
        videoStreamInfoService.deleteVideoStreamInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得视频流信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:query')")
    public CommonResult<VideoStreamInfoRespVO> getVideoStreamInfo(@RequestParam("id") Long id) {
        VideoStreamInfoDO videoStreamInfo = videoStreamInfoService.getVideoStreamInfo(id);
        VideoStreamInfoRespVO bean = BeanUtils.toBean(videoStreamInfo, VideoStreamInfoRespVO.class);
        bean.setCamera(BeanUtils.toBean(cameraInfoService.getCameraInfo(bean.getCameraId()), CameraInfoRespVO.class));
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得视频流信息分页")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:query')")
    public CommonResult<PageResult<VideoStreamInfoRespVO>> getVideoStreamInfoPage(@Valid VideoStreamInfoPageReqVO pageReqVO) {
        PageResult<VideoStreamInfoDO> pageResult = videoStreamInfoService.getVideoStreamInfoPage(pageReqVO);
        CameraInfoPageReqVO cameraInfoPageReqVO = new CameraInfoPageReqVO();
        cameraInfoPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<VideoStreamInfoRespVO> page = BeanUtils.toBean(pageResult, VideoStreamInfoRespVO.class);
        if (!CollectionUtil.isEmpty(page.getList())) {
            List<CameraInfoDO> list = cameraInfoService.getCameraInfoPage(cameraInfoPageReqVO).getList();
            Map<Long, CameraInfoDO> map = list.stream().collect(Collectors.toMap(CameraInfoDO::getId, Function.identity()));
            page.getList().stream().forEach(item -> {
                CameraInfoDO camera = map.get(item.getCameraId());
                item.setCamera(camera == null ? null : BeanUtils.toBean(camera, CameraInfoRespVO.class));
            });
        }
        return success(page);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出视频流信息 Excel")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVideoStreamInfoExcel(@Valid VideoStreamInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VideoStreamInfoDO> list = videoStreamInfoService.getVideoStreamInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "视频流信息.xls", "数据", VideoStreamInfoRespVO.class,
                        BeanUtils.toBean(list, VideoStreamInfoRespVO.class));
    }

    @GetMapping("/video-stream-list")
    @Operation(summary = "获得视频流信息列表")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:query')")
    public CommonResult<List<VideoStreamInfoRespVO>> getVideoStreamList(@Valid VideoStreamInfoPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VideoStreamInfoDO> list = videoStreamInfoService.getVideoStreamInfoPage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, VideoStreamInfoRespVO.class));
    }

    @GetMapping("/active-streams")
    @Operation(summary = "获取所有活跃的视频流及其图像数据")
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:query')")
    public CommonResult<List<Map<String, Object>>> getActiveStreamsWithImages() {
        // 获取所有未删除的视频流信息
        VideoStreamInfoPageReqVO pageReqVO = new VideoStreamInfoPageReqVO();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VideoStreamInfoDO> streams = videoStreamInfoService.getVideoStreamInfoPage(pageReqVO).getList()
                .stream()
                .filter(stream -> !Boolean.TRUE.equals(stream.getDeleted()))
                .collect(Collectors.toList());

        List<Map<String, Object>> result = new ArrayList<>();

        // 为每个视频流获取Redis中的图像数据
        for (VideoStreamInfoDO stream : streams) {
            Map<String, Object> streamData = new HashMap<>();
            streamData.put("id", stream.getId());
            streamData.put("cameraId", stream.getCameraId());
            streamData.put("concernedArea", stream.getConcernedArea());
            streamData.put("name", stream.getName());

            // 构建Redis键
            String redisKey = "het_img:" + stream.getId() + ":" + stream.getCameraId();

            // 从Redis获取图像数据
            String base64Image = (String) redisTemplate.opsForHash().get(redisKey, "img_byte");
                streamData.put("imageBase64", base64Image);

            result.add(streamData);
        }

        return success(result);
    }

    @GetMapping("/stream-image")
    @Operation(summary = "获取单个视频流的图像数据")
    @Parameter(name = "id", description = "视频流ID", required = true)
    @Parameter(name = "cameraId", description = "摄像头ID", required = true)
    @PreAuthorize("@ss.hasPermission('safe:video-stream-info:query')")
    public CommonResult<Map<String, Object>> getStreamImage(@RequestParam("id") Long id, @RequestParam("cameraId") Long cameraId) {
        Map<String, Object> result = new HashMap<>();
        // 构建Redis键
        String redisKey = "het_img:" + id + ":" + cameraId;
        // 从Redis获取图像数据
        String base64Image = (String) redisTemplate.opsForHash().get(redisKey, "img_byte");
        result.put("imageBase64", base64Image);
        // 获取其他相关数据
        Object personCount = redisTemplate.opsForHash().get(redisKey, "person_count");
        Object helmetCount = redisTemplate.opsForHash().get(redisKey, "helmet_count");
        Object gapCount = redisTemplate.opsForHash().get(redisKey, "gap_person_and_helmet");
        Object updateTime = redisTemplate.opsForHash().get(redisKey, "update_time");

        if (personCount != null) result.put("personCount", personCount);
        if (helmetCount != null) result.put("helmetCount", helmetCount);
        if (gapCount != null) result.put("gapCount", gapCount);
        if (updateTime != null) result.put("updateTime", updateTime);

        return success(result);
    }
}
