package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 定员分析历史数据 VO
 */
@Schema(description = "定员分析历史数据 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuotaAnalysisHistoryDataVO extends QuotaAnalysisBaseVO {

    @Schema(description = "区域编号")
    private Long areaId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "记录时间")
    private String recordTime;

    @Schema(description = "人数")
    private Integer personCount;

    @Schema(description = "厂区编号")
    private Long factoryId;
} 