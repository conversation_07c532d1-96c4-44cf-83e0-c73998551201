package com.nj9394.module.safe.controller.admin.datastorage;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.datastorage.vo.*;
import com.nj9394.module.safe.dal.dataobject.datastorage.DataStorageDO;
import com.nj9394.module.safe.service.datastorage.DataStorageService;

@Tag(name = "管理后台 - 数据存储")
@RestController
@RequestMapping("/safe/data-storage")
@Validated
public class DataStorageController {

    @Resource
    private DataStorageService dataStorageService;

    @PostMapping("/create")
    @Operation(summary = "创建数据存储")
    @PreAuthorize("@ss.hasPermission('safe:data-storage:create')")
    public CommonResult<Long> createDataStorage(@Valid @RequestBody DataStorageSaveReqVO createReqVO) {
        return success(dataStorageService.createDataStorage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新数据存储")
    @PreAuthorize("@ss.hasPermission('safe:data-storage:update')")
    public CommonResult<Boolean> updateDataStorage(@Valid @RequestBody DataStorageSaveReqVO updateReqVO) {
        dataStorageService.updateDataStorage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除数据存储")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:data-storage:delete')")
    public CommonResult<Boolean> deleteDataStorage(@RequestParam("id") Long id) {
        dataStorageService.deleteDataStorage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得数据存储")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:data-storage:query')")
    public CommonResult<DataStorageRespVO> getDataStorage(@RequestParam("id") Long id) {
        DataStorageDO dataStorage = dataStorageService.getDataStorage(id);
        return success(BeanUtils.toBean(dataStorage, DataStorageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得数据存储分页")
    @PreAuthorize("@ss.hasPermission('safe:data-storage:query')")
    public CommonResult<PageResult<DataStorageRespVO>> getDataStoragePage(@Valid DataStoragePageReqVO pageReqVO) {
        PageResult<DataStorageDO> pageResult = dataStorageService.getDataStoragePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DataStorageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出数据存储 Excel")
    @PreAuthorize("@ss.hasPermission('safe:data-storage:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDataStorageExcel(@Valid DataStoragePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DataStorageDO> list = dataStorageService.getDataStoragePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "数据存储.xls", "数据", DataStorageRespVO.class,
                        BeanUtils.toBean(list, DataStorageRespVO.class));
    }

}