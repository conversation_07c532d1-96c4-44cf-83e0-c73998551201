package com.nj9394.module.safe.controller.admin.videostreaminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 视频流信息新增/修改 Request VO")
@Data
public class VideoStreamInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23123")
    private Long id;

    @Schema(description = "摄像头编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3472")
    @NotNull(message = "摄像头编号不能为空")
    private Long cameraId;

    @Schema(description = "RTSP地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "RTSP地址不能为空")
    private String rtspAddress;

    @Schema(description = "视频质量")
    private String videoQuality;

    @Schema(description = "关注区域（存储区域的坐标或描述信息）")
    private String concernedArea;

    @Schema(description = "别名")
    private String name;

}