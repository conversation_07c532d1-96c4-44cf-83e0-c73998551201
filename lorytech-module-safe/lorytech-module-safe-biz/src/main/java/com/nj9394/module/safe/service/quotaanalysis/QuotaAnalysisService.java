package com.nj9394.module.safe.service.quotaanalysis;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.*;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * 定员分析 Service 接口
 *
 * <AUTHOR>
 */
public interface QuotaAnalysisService {

    /**
     * 创建定员分析
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuotaAnalysis(@Valid QuotaAnalysisSaveReqVO createReqVO);

    /**
     * 更新定员分析
     *
     * @param updateReqVO 更新信息
     */
    void updateQuotaAnalysis(@Valid QuotaAnalysisSaveReqVO updateReqVO);

    /**
     * 删除定员分析
     *
     * @param id 编号
     */
    void deleteQuotaAnalysis(Long id);

    /**
     * 获得定员分析
     *
     * @param id 编号
     * @return 定员分析
     */
    QuotaAnalysisDO getQuotaAnalysis(Long id);

    /**
     * 获得定员分析分页
     *
     * @param pageReqVO 分页查询
     * @return 定员分析分页
     */
    PageResult<QuotaAnalysisDO> getQuotaAnalysisPage(QuotaAnalysisPageReqVO pageReqVO);

    List<QuotaAnalysisDO> getQuotaAnalysisList(Long areaId, Date stime, Date etime);
    
    /**
     * 获取各个厂区的人数统计数据
     * 
     * @return 各个厂区的人数统计数据列表
     */
    List<QuotaAnalysisFactoryStatsRespVO> getFactoryPersonStats();
    
    /**
     * 获取总人数
     * 
     * @return 总人数
     */
    int getTotalPersonCount();
    
    /**
     * 获取视频列表数据
     * 
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @return 视频列表数据
     */
    List<QuotaAnalysisVideoListRespVO> getVideoList(Long factoryId);

    /**
     * 获取历史人数数据
     * 
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @param days 查询天数，默认30天
     * @return 历史人数数据
     */
    Map<String, Object> getHistoryPersonCount(Long factoryId, Integer days);
    
    /**
     * 获取厂区人流量数据
     * 
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @return 厂区人流量数据
     */
    QuotaAnalysisFlowRateRespVO getFactoryFlowRate(Long factoryId);
}