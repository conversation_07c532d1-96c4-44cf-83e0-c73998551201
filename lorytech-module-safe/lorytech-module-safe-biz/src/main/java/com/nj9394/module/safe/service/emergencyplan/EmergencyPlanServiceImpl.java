package com.nj9394.module.safe.service.emergencyplan;

import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.nj9394.module.safe.controller.admin.emergencyplan.vo.*;
import com.nj9394.module.safe.dal.dataobject.emergencyplan.EmergencyPlanDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.emergencyplan.EmergencyPlanMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertMap;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 应急预案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmergencyPlanServiceImpl implements EmergencyPlanService {

    @Resource
    private EmergencyPlanMapper emergencyPlanMapper;

    @Override
    public Long createEmergencyPlan(EmergencyPlanSaveReqVO createReqVO) {
        // 插入
        EmergencyPlanDO emergencyPlan = BeanUtils.toBean(createReqVO, EmergencyPlanDO.class);
        emergencyPlanMapper.insert(emergencyPlan);
        // 返回
        return emergencyPlan.getId();
    }

    @Override
    public void updateEmergencyPlan(EmergencyPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateEmergencyPlanExists(updateReqVO.getId());
        // 更新
        EmergencyPlanDO updateObj = BeanUtils.toBean(updateReqVO, EmergencyPlanDO.class);
        emergencyPlanMapper.updateById(updateObj);
    }

    @Override
    public void deleteEmergencyPlan(Long id) {
        // 校验存在
        validateEmergencyPlanExists(id);
        // 删除
        emergencyPlanMapper.deleteById(id);
    }

    private void validateEmergencyPlanExists(Long id) {
        if (emergencyPlanMapper.selectById(id) == null) {
            throw exception(EMERGENCY_PLAN_NOT_EXISTS);
        }
    }

    @Override
    public EmergencyPlanDO getEmergencyPlan(Long id) {
        return emergencyPlanMapper.selectById(id);
    }

    @Override
    public PageResult<EmergencyPlanDO> getEmergencyPlanPage(EmergencyPlanPageReqVO pageReqVO) {
        return emergencyPlanMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<Long, EmergencyPlanRespVO> getPlanMap(Collection<Long> ids) {
        List<EmergencyPlanRespVO> list;
        if (CollUtil.isEmpty(ids)) {
            list = Collections.emptyList();
        }
        else {
            list = emergencyPlanMapper.findListByIds(ids);
        }
        return convertMap(list, EmergencyPlanRespVO::getId);
    }

}