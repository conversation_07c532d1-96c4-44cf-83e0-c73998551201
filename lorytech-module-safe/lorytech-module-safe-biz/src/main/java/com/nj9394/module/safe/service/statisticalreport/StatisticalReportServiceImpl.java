package com.nj9394.module.safe.service.statisticalreport;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.excel.EasyExcel;
import com.nj9394.module.infra.api.file.FileApi;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisRespVO;
import com.nj9394.module.safe.dal.mysql.areadivision.AreaDivisionMapper;
import com.nj9394.module.safe.dal.mysql.persondangerinfo.PersonDangerInfoMapper;
import com.nj9394.module.safe.dal.mysql.quotaanalysis.QuotaAnalysisMapper;
import com.nj9394.module.safe.enums.EnumReportType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.nj9394.module.safe.controller.admin.statisticalreport.vo.*;
import com.nj9394.module.safe.dal.dataobject.statisticalreport.StatisticalReportDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.statisticalreport.StatisticalReportMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertMap;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 统计报表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StatisticalReportServiceImpl implements StatisticalReportService {

    @Resource
    private StatisticalReportMapper statisticalReportMapper;

    @Resource
    private AreaDivisionMapper areaDivisionMapper;

    @Resource
    private QuotaAnalysisMapper quotaAnalysisMapper;

    @Resource
    private PersonDangerInfoMapper personDangerInfoMapper;

    @Resource
    private FileApi fileApi;

    @Value("${lorytech.temp-path}")
    private String TEMP_PATH;

    @Override
    public Long createStatisticalReport(StatisticalReportSaveReqVO createReqVO) {
        // 插入
        StatisticalReportDO statisticalReport = BeanUtils.toBean(createReqVO, StatisticalReportDO.class);
        statisticalReportMapper.insert(statisticalReport);
        // 返回
        return statisticalReport.getId();
    }

    @Override
    public void updateStatisticalReport(StatisticalReportSaveReqVO updateReqVO) {
        // 校验存在
        validateStatisticalReportExists(updateReqVO.getId());
        // 更新
        StatisticalReportDO updateObj = BeanUtils.toBean(updateReqVO, StatisticalReportDO.class);
        statisticalReportMapper.updateById(updateObj);
    }

    @Override
    public void deleteStatisticalReport(Long id) {
        // 校验存在
        validateStatisticalReportExists(id);
        // 删除
        statisticalReportMapper.deleteById(id);
    }

    private void validateStatisticalReportExists(Long id) {
        if (statisticalReportMapper.selectById(id) == null) {
            throw exception(STATISTICAL_REPORT_NOT_EXISTS);
        }
    }

    @Override
    public StatisticalReportDO getStatisticalReport(Long id) {
        return statisticalReportMapper.selectById(id);
    }

    @Override
    public PageResult<StatisticalReportDO> getStatisticalReportPage(StatisticalReportPageReqVO pageReqVO) {
        return statisticalReportMapper.selectPage(pageReqVO);
    }

    @Override
    public String createReport(String reportType, LocalDateTime stime, LocalDateTime etime) {
        StatisticalReportSaveReqVO createReqVO = new StatisticalReportSaveReqVO();
        createReqVO.setReportType(reportType);
        createReqVO.setReportStime(stime);
        createReqVO.setReportEtime(etime);

        if (reportType.equals(EnumReportType.DAY.getType())) {
            // 日报
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String day = createReqVO.getReportStime().format(formatter);
            createReqVO.setReportTitle(day + "日统计报表");
        }
        else if (reportType.equals(EnumReportType.MONTH.getType())) {
            // 月报
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            String month = createReqVO.getReportStime().format(formatter);
            createReqVO.setReportTitle(month + "月统计报表");
        }
        else {
            // 年报
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
            String year = createReqVO.getReportStime().format(formatter);
            createReqVO.setReportTitle(year + "年统计报表");
        }

        // 查询报表是否存在
        Long num = statisticalReportMapper.selectCount("report_title", createReqVO.getReportTitle());
        if (num > 0) {
            return "生成报表失败：报表已经存在";
        }

        // 统计数据集合
        Map<Long, StatisticalReportInfoVO> dataMap = new HashMap<>();

        // 区域列表
        List<AreaDivisionRespVO> areaList = areaDivisionMapper.findAllList();
        if (areaList != null && areaList.size() > 0) {
            for (AreaDivisionRespVO area : areaList) {
                StatisticalReportInfoVO reportInfoVO = new StatisticalReportInfoVO();
                reportInfoVO.setFactoryName(area.getFactoryName());
                reportInfoVO.setAreaName(area.getName());
                dataMap.put(area.getId(), reportInfoVO);
            }
        }
        else {
            return "生成报表失败：未找到分析区域信息";
        }

        // 定员分析数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<Map<String, Object>> countList = quotaAnalysisMapper.selectCountList(createReqVO.getReportStime().format(formatter), createReqVO.getReportEtime().format(formatter));
        if (countList != null && countList.size() > 0) {
            for (Map<String, Object> map : countList) {
                Long areaId = (Long) map.get("area_id");
                Long count = (Long) map.get("count");
                if (dataMap.containsKey(areaId)) {
                    Integer overCount = dataMap.get(areaId).getOverCount();
                    overCount = overCount + count.intValue();
                    dataMap.get(areaId).setOverCount(overCount);
                }
            }
        }

        countList = quotaAnalysisMapper.selectAlarmCountList(createReqVO.getReportStime().format(formatter), createReqVO.getReportEtime().format(formatter));
        if (countList != null && countList.size() > 0) {
            for (Map<String, Object> map : countList) {
                Long areaId = (Long) map.get("area_id");
                Integer level = (Integer) map.get("alert_level");
                Long count = (Long) map.get("count");
                if (dataMap.containsKey(areaId)) {
                    StatisticalReportInfoVO reportInfoVO = dataMap.get(areaId);
                    if (level == 1) {
                        Integer overCount = reportInfoVO.getOverAlarmCount();
                        overCount = overCount + count.intValue();
                        dataMap.get(areaId).setOverAlarmCount(overCount);
                    }
                    else if (level == 2) {
                        Integer overCount = reportInfoVO.getOverDangerCount();
                        overCount = overCount + count.intValue();
                        dataMap.get(areaId).setOverDangerCount(overCount);
                    }
                    else if (level == 3) {
                        Integer overCount = reportInfoVO.getOverSeriousCount();
                        overCount = overCount + count.intValue();
                        dataMap.get(areaId).setOverSeriousCount(overCount);
                    }
                }
            }
        }

        // 人员安全分析
        countList = personDangerInfoMapper.selectCountList(createReqVO.getReportStime().format(formatter), createReqVO.getReportEtime().format(formatter));
        if (countList != null && countList.size() > 0) {
            for (Map<String, Object> map : countList) {
                Long areaId = (Long) map.get("area_id");
                Long count = (Long) map.get("count");
                if (dataMap.containsKey(areaId)) {
                    Integer overCount = dataMap.get(areaId).getPersonCount();
                    overCount = overCount + count.intValue();
                    dataMap.get(areaId).setPersonCount(overCount);
                }
            }
        }

        countList = personDangerInfoMapper.selectAlarmCountList(createReqVO.getReportStime().format(formatter), createReqVO.getReportEtime().format(formatter));
        if (countList != null && countList.size() > 0) {
            for (Map<String, Object> map : countList) {
                Long areaId = (Long) map.get("area_id");
                Long count = (Long) map.get("count");
                if (dataMap.containsKey(areaId)) {
                    Integer overCount = dataMap.get(areaId).getPersonNoCount();
                    overCount = overCount + count.intValue();
                    dataMap.get(areaId).setPersonNoCount(overCount);
                }
            }
        }

        // 生成统计文件
        // 文件地址
        String fileName = MD5.create().digestHex(createReqVO.getReportTitle()) + ".xls";
        String filePath = TEMP_PATH + File.separator + fileName;

        // 输出Excel
        EasyExcel.write(filePath, StatisticalReportInfoVO.class).sheet("安全分析统计数据").doWrite(dataMap.values());
        File file = new File(filePath);
        if (file.exists()) {
            // 保存地址
            createReqVO.setReportFile(fileApi.createFile(fileName, FileUtil.readBytes(file)));
            file.delete();
        }

        StatisticalReportDO statisticalReport = BeanUtils.toBean(createReqVO, StatisticalReportDO.class);
        statisticalReport.setCreator("1");
        statisticalReport.setUpdater("1");
        statisticalReport.setDeleted(false);
        statisticalReport.setTenantId(1L);
        statisticalReportMapper.insert(statisticalReport);

        return "生成统计报表成功";

    }

}