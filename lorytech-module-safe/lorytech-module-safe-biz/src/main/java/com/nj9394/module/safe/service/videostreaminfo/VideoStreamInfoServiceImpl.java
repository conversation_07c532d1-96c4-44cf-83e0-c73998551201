package com.nj9394.module.safe.service.videostreaminfo;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.videostreaminfo.VideoStreamInfoDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.videostreaminfo.VideoStreamInfoMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 视频流信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VideoStreamInfoServiceImpl implements VideoStreamInfoService {

    @Resource
    private VideoStreamInfoMapper videoStreamInfoMapper;

    @Override
    public Long createVideoStreamInfo(VideoStreamInfoSaveReqVO createReqVO) {
        // 插入
        VideoStreamInfoDO videoStreamInfo = BeanUtils.toBean(createReqVO, VideoStreamInfoDO.class);
        videoStreamInfoMapper.insert(videoStreamInfo);
        // 返回
        return videoStreamInfo.getId();
    }

    @Override
    public void updateVideoStreamInfo(VideoStreamInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateVideoStreamInfoExists(updateReqVO.getId());
        // 更新
        VideoStreamInfoDO updateObj = BeanUtils.toBean(updateReqVO, VideoStreamInfoDO.class);
        videoStreamInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteVideoStreamInfo(Long id) {
        // 校验存在
        validateVideoStreamInfoExists(id);
        // 删除
        videoStreamInfoMapper.deleteById(id);
    }

    private void validateVideoStreamInfoExists(Long id) {
        if (videoStreamInfoMapper.selectById(id) == null) {
            throw exception(VIDEO_STREAM_INFO_NOT_EXISTS);
        }
    }

    @Override
    public VideoStreamInfoDO getVideoStreamInfo(Long id) {
        return videoStreamInfoMapper.selectById(id);
    }

    @Override
    public PageResult<VideoStreamInfoDO> getVideoStreamInfoPage(VideoStreamInfoPageReqVO pageReqVO) {
        return videoStreamInfoMapper.selectPage(pageReqVO);
    }
    
    @Override
    public List<VideoStreamInfoDO> getWorkingVideoStreamList() {
        return videoStreamInfoMapper.selectWorkingList();
    }

    @Override
    public List<Map<String, Object>> getWorkingVideoStreamWithAreaList() {
        return videoStreamInfoMapper.selectWorkingVideoStreamWithAreaList();
    }
}