package com.nj9394.module.safe.controller.admin.statisticalreport.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 统计报表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalReportInfoVO {

    @Schema(description = "厂房")
    @ExcelProperty("厂房")
    private String factoryName;

    @Schema(description = "区域名称")
    @ExcelProperty("区域名称")
    private String areaName;

    @Schema(description = "定员分析次数")
    @ExcelProperty("定员分析次数")
    private Integer quotaCount = 0;

    @Schema(description = "超员次数")
    @ExcelProperty("超员次数")
    private Integer overCount = 0;

    @Schema(description = "告警级别超员次数")
    @ExcelProperty("告警级别超员次数")
    private Integer overAlarmCount = 0;

    @Schema(description = "危险级别超员次数")
    @ExcelProperty("危险级别超员次数")
    private Integer overDangerCount = 0;

    @Schema(description = "严重级别超员次数")
    @ExcelProperty("严重级别超员次数")
    private Integer overSeriousCount = 0;

    @Schema(description = "人员佩戴安全分析次数")
    @ExcelProperty("人员佩戴安全分析次数")
    private Integer personCount = 0;

    @Schema(description = "人员未佩戴安全帽次数")
    @ExcelProperty("人员未佩戴安全帽次数")
    private Integer personNoCount = 0;

}
