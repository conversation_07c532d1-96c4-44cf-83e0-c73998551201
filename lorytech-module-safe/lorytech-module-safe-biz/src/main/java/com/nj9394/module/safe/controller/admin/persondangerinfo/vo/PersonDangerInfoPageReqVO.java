package com.nj9394.module.safe.controller.admin.persondangerinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.nj9394.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.nj9394.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 人员危险情况分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonDangerInfoPageReqVO extends PageParam {

    @Schema(description = "区域编号", example = "8225")
    private Long areaId;

    @Schema(description = "是否佩戴安全帽")
    private Integer isWearingHelmet;

    @Schema(description = "是否穿着工作服")
    private Boolean isWearingWorkClothes;

    @Schema(description = "是否出现危险行为")
    private Integer isDangerousBehavior;

    @Schema(description = "是否出现危险情况")
    private Boolean isDangerousSituation;

    @Schema(description = "记录时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] recordTime;

    @Schema(description = "记录文件")
    private String recordFile;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
