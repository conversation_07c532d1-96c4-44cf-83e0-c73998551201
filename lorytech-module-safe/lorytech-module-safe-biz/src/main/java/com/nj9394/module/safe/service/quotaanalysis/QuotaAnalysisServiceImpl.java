package com.nj9394.module.safe.service.quotaanalysis;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.stream.Collectors;

import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.*;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.quotaanalysis.QuotaAnalysisMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 定员分析 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuotaAnalysisServiceImpl implements QuotaAnalysisService {

    @Resource
    private QuotaAnalysisMapper quotaAnalysisMapper;

    @Override
    public Long createQuotaAnalysis(QuotaAnalysisSaveReqVO createReqVO) {
        // 插入
        QuotaAnalysisDO quotaAnalysis = BeanUtils.toBean(createReqVO, QuotaAnalysisDO.class);
        quotaAnalysisMapper.insert(quotaAnalysis);
        // 返回
        return quotaAnalysis.getId();
    }

    @Override
    public void updateQuotaAnalysis(QuotaAnalysisSaveReqVO updateReqVO) {
        // 校验存在
        validateQuotaAnalysisExists(updateReqVO.getId());
        // 更新
        QuotaAnalysisDO updateObj = BeanUtils.toBean(updateReqVO, QuotaAnalysisDO.class);
        quotaAnalysisMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuotaAnalysis(Long id) {
        // 校验存在
        validateQuotaAnalysisExists(id);
        // 删除
        quotaAnalysisMapper.deleteById(id);
    }

    private void validateQuotaAnalysisExists(Long id) {
        if (quotaAnalysisMapper.selectById(id) == null) {
            throw exception(QUOTA_ANALYSIS_NOT_EXISTS);
        }
    }

    @Override
    public QuotaAnalysisDO getQuotaAnalysis(Long id) {
        return quotaAnalysisMapper.selectById(id);
    }

    @Override
    public PageResult<QuotaAnalysisDO> getQuotaAnalysisPage(QuotaAnalysisPageReqVO pageReqVO) {
        return quotaAnalysisMapper.selectPage(pageReqVO);
    }

    @Override
    public List<QuotaAnalysisDO> getQuotaAnalysisList(Long areaId, Date stime, Date etime) {
        return null;
    }

    @Override
    public List<QuotaAnalysisFactoryStatsRespVO> getFactoryPersonStats() {
        // 获取当前日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 查询各个工厂的人数统计
        List<Map<String, Object>> factoryStats = quotaAnalysisMapper.selectFactoryPersonStats(today);
        if (factoryStats == null || factoryStats.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取总人数
        Integer totalCount = getTotalPersonCount();
        if (totalCount == null || totalCount == 0) {
            return Collections.emptyList();
        }

        // 转换为响应对象
        List<QuotaAnalysisFactoryStatsRespVO> result = new ArrayList<>(factoryStats.size());
        for (Map<String, Object> stat : factoryStats) {
            QuotaAnalysisFactoryStatsRespVO vo = new QuotaAnalysisFactoryStatsRespVO();

            // 设置工厂ID
            Object factoryId = stat.get("factory_id");
            if (factoryId instanceof Number) {
                vo.setFactoryId(((Number) factoryId).longValue());
            }

            // 设置工厂名称
            vo.setFactoryName((String) stat.get("factory_name"));

            // 设置人数
            Object personCount = stat.get("person_count");
            if (personCount instanceof Number) {
                int count = ((Number) personCount).intValue();
                vo.setPersonCount(count);

                // 计算百分比
                double percentage = (double) count / totalCount * 100;
                vo.setPercentage(Math.round(percentage * 100) / 100.0); // 保留两位小数
            } else {
                vo.setPersonCount(0);
                vo.setPercentage(0.0);
            }
            result.add(vo);
        }

        return result;
    }

    @Override
    public int getTotalPersonCount() {
        // 获取当前日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 查询总人数
        Integer totalCount = quotaAnalysisMapper.selectTotalPersonCount(today);
        return totalCount != null ? totalCount : 0;
    }

    @Override
    public List<QuotaAnalysisVideoListRespVO> getVideoList(Long factoryId) {
        // 获取当前日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 查询视频列表数据
        List<Map<String, Object>> videoList = quotaAnalysisMapper.selectVideoList(today, factoryId);
        if (videoList == null || videoList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 转换为响应对象
        List<QuotaAnalysisVideoListRespVO> result = new ArrayList<>(videoList.size());
        for (Map<String, Object> video : videoList) {
            QuotaAnalysisVideoListRespVO vo = new QuotaAnalysisVideoListRespVO();
            
            // 设置区域ID
            Object areaId = video.get("area_id");
            if (areaId instanceof Number) {
                vo.setAreaId(((Number) areaId).longValue());
            }
            
            // 设置区域名称
            vo.setAreaName((String) video.get("area_name"));
            
            // 设置人数上限
            Object maxAllowance = video.get("max_allowance");
            if (maxAllowance instanceof Number) {
                vo.setMaxAllowance(((Number) maxAllowance).intValue());
            } else {
                vo.setMaxAllowance(0);
            }
            
            // 设置视频图片地址
            vo.setRecordPic((String) video.get("record_pic"));
            
            // 设置实际人数
            Object currentPersonCount = video.get("current_person_count");
            if (currentPersonCount instanceof Number) {
                vo.setCurrentPersonCount(((Number) currentPersonCount).intValue());
            } else {
                vo.setCurrentPersonCount(0);
            }
            
            // 设置厂区ID
            Object factoryIdObj = video.get("factory_id");
            if (factoryIdObj instanceof Number) {
                vo.setFactoryId(((Number) factoryIdObj).longValue());
            }
            
            result.add(vo);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getHistoryPersonCount(Long factoryId, Integer days) {
        // 计算查询的开始时间（当前时间减去指定天数）
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        
        // 查询历史人数数据
        List<QuotaAnalysisHistoryDataVO> historyData = quotaAnalysisMapper.selectHistoryPersonCount(startTime, factoryId);
        
        // 按厂区ID分组
        Map<Long, List<QuotaAnalysisHistoryDataVO>> groupedByFactory = new HashMap<>();
        
        // 按厂区分组
        for (QuotaAnalysisHistoryDataVO data : historyData) {
            Long areaFactoryId = data.getFactoryId();
            
            if (!groupedByFactory.containsKey(areaFactoryId)) {
                groupedByFactory.put(areaFactoryId, new ArrayList<>());
            }
            
            groupedByFactory.get(areaFactoryId).add(data);
        }
        
        // 准备返回结果
        Map<String, Object> result = new HashMap<>();
        
        // 处理每个厂区的数据
        List<Map<String, Object>> factoryDataList = new ArrayList<>();
        for (Map.Entry<Long, List<QuotaAnalysisHistoryDataVO>> entry : groupedByFactory.entrySet()) {
            Long factoryIdKey = entry.getKey();
            List<QuotaAnalysisHistoryDataVO> factoryData = entry.getValue();
            
            // 按区域分组
            Map<Long, List<QuotaAnalysisHistoryDataVO>> groupedByArea = factoryData.stream()
                    .collect(Collectors.groupingBy(QuotaAnalysisHistoryDataVO::getAreaId));
            
            // 处理每个区域的数据
            List<Map<String, Object>> areaDataList = new ArrayList<>();
            for (Map.Entry<Long, List<QuotaAnalysisHistoryDataVO>> areaEntry : groupedByArea.entrySet()) {
                Long areaId = areaEntry.getKey();
                List<QuotaAnalysisHistoryDataVO> areaData = areaEntry.getValue();
                
                // 获取区域名称
                String areaName = areaData.get(0).getAreaName();
                
                // 按时间排序
                areaData.sort(Comparator.comparing(QuotaAnalysisHistoryDataVO::getRecordTime));
                
                // 如果数据点太多，进行采样
                List<QuotaAnalysisHistoryDataVO> sampledData = sampleData(areaData, 100); // 最多返回100个数据点
                
                // 转换为前端需要的格式
                List<Map<String, Object>> timeSeriesData = new ArrayList<>();
                for (QuotaAnalysisHistoryDataVO item : sampledData) {
                    Map<String, Object> timeSeriesItem = new HashMap<>();
                    timeSeriesItem.put("time", item.getRecordTime());
                    timeSeriesItem.put("count", item.getPersonCount());
                    timeSeriesItem.put("areaId", item.getAreaId());
                    timeSeriesItem.put("areaName", item.getAreaName());
                    timeSeriesData.add(timeSeriesItem);
                }
                
                // 构建区域数据
                Map<String, Object> areaInfo = new HashMap<>();
                areaInfo.put("areaId", areaId);
                areaInfo.put("areaName", areaName);
                areaInfo.put("data", timeSeriesData);
                
                areaDataList.add(areaInfo);
            }
            
            // 构建厂区数据
            Map<String, Object> factoryInfo = new HashMap<>();
            factoryInfo.put("factoryId", factoryIdKey);
            factoryInfo.put("areas", areaDataList);
            
            factoryDataList.add(factoryInfo);
        }
        
        result.put("factories", factoryDataList);
        
        return result;
    }

    /**
     * 对数据进行采样，减少数据点数量
     * 
     * @param data 原始数据
     * @param maxPoints 最大数据点数量
     * @return 采样后的数据
     */
    private List<QuotaAnalysisHistoryDataVO> sampleData(List<QuotaAnalysisHistoryDataVO> data, int maxPoints) {
        if (data.size() <= maxPoints) {
            return data; // 如果数据点数量不超过最大值，直接返回
        }
        
        List<QuotaAnalysisHistoryDataVO> result = new ArrayList<>();
        int step = data.size() / maxPoints;
        
        for (int i = 0; i < data.size(); i += step) {
            result.add(data.get(i));
            
            // 确保最后一个点被添加
            if (result.size() == maxPoints - 1 && i + step >= data.size()) {
                result.add(data.get(data.size() - 1));
                break;
            }
        }
        
        return result;
    }

    @Override
    public QuotaAnalysisFlowRateRespVO getFactoryFlowRate(Long factoryId) {
        // 查询厂区人流量数据
        Map<String, Object> flowRateData = quotaAnalysisMapper.selectFactoryFlowRate(factoryId);
        
        // 构建返回结果
        QuotaAnalysisFlowRateRespVO result = new QuotaAnalysisFlowRateRespVO();
        
        if (flowRateData != null) {
            // 设置当前人数
            Object currentCount = flowRateData.get("current_count");
            if (currentCount != null) {
                result.setCurrentCount(((Number) currentCount).intValue());
            } else {
                result.setCurrentCount(0);
            }
            
            // 设置最大允许人数
            Object maxAllowance = flowRateData.get("max_allowance");
            if (maxAllowance != null) {
                result.setMaxAllowance(((Number) maxAllowance).intValue());
            } else {
                result.setMaxAllowance(100); // 默认值
            }
            
            // 设置最大限制人数
            Object maxLimit = flowRateData.get("max_limit");
            if (maxLimit != null) {
                result.setMaxLimit(((Number) maxLimit).intValue());
            } else {
                result.setMaxLimit(200); // 默认值
            }
            
            // 设置最大严重人数
            Object maxSerious = flowRateData.get("max_serious");
            if (maxSerious != null) {
                result.setMaxSerious(((Number) maxSerious).intValue());
            } else {
                result.setMaxSerious(300); // 默认值
            }
        } else {
            // 设置默认值
            result.setCurrentCount(0);
            result.setMaxAllowance(100);
            result.setMaxLimit(200);
            result.setMaxSerious(300);
        }
        
        return result;
    }
}
