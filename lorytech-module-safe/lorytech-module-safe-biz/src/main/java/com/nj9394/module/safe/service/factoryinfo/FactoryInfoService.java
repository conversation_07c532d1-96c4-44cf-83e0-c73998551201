package com.nj9394.module.safe.service.factoryinfo;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoSaveReqVO;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;

import javax.validation.Valid;

/**
 * 厂房信息 Service 接口
 *
 * <AUTHOR>
 */
public interface FactoryInfoService {

    /**
     * 创建厂房信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFactoryInfo(@Valid FactoryInfoSaveReqVO createReqVO);

    /**
     * 更新厂房信息
     *
     * @param updateReqVO 更新信息
     */
    void updateFactoryInfo(@Valid FactoryInfoSaveReqVO updateReqVO);

    /**
     * 删除厂房信息
     *
     * @param id 编号
     */
    void deleteFactoryInfo(Long id);

    /**
     * 获得厂房信息
     *
     * @param id 编号
     * @return 厂房信息
     */
    FactoryInfoDO getFactoryInfo(Long id);

    /**
     * 获得厂房信息分页
     *
     * @param pageReqVO 分页查询
     * @return 厂房信息分页
     */
    PageResult<FactoryInfoDO> getFactoryInfoPage(FactoryInfoPageReqVO pageReqVO);

}