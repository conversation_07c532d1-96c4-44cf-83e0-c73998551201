package com.nj9394.module.safe.dal.mysql.modelexecutionrecord;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.modelexecutionrecord.ModelExecutionRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo.*;

/**
 * 模型执行 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelExecutionRecordMapper extends BaseMapperX<ModelExecutionRecordDO> {

    default PageResult<ModelExecutionRecordDO> selectPage(ModelExecutionRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelExecutionRecordDO>()
                .eqIfPresent(ModelExecutionRecordDO::getModelId, reqVO.getModelId())
                .betweenIfPresent(ModelExecutionRecordDO::getExecutionTime, reqVO.getExecutionTime())
                .betweenIfPresent(ModelExecutionRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelExecutionRecordDO::getId));
    }

}