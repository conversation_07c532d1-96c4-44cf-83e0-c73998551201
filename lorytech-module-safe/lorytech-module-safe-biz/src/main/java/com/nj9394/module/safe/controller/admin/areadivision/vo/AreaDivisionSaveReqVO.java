package com.nj9394.module.safe.controller.admin.areadivision.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 区域划分新增/修改 Request VO")
@Data
public class AreaDivisionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22570")
    private Long id;

    @Schema(description = "厂房编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7447")
    @NotNull(message = "厂房编号不能为空")
    private Long factoryId;

    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "区域名称不能为空")
    private String name;

    @Schema(description = "最大允许人数")
    private Integer maxAllowance;

    @Schema(description = "危险等级")
    private String dangerLevel;

    @Schema(description = "区域")
    private String area;

    @Schema(description = "画布更改后的图片")
    private String areaImgPath;

    @Schema(description = "最大超限人数")
    private Integer maxLimit;

    @Schema(description = "严重超限人数")
    private Integer maxSerious;

}
