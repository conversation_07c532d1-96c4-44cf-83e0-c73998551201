package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;
import com.nj9394.module.safe.utils.ImagePathConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 定员分析 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuotaAnalysisRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30802")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6850")
    private Long areaId;

    @Schema(description = "厂房")
    @ExcelProperty("厂房")
    private String factoryName;

    @Schema(description = "区域名称")
    @ExcelProperty("区域名称")
    private String areaName;

    @Schema(description = "当前人员数量", example = "23753")
    @ExcelProperty("当前人员数量")
    private Integer currentPersonCount;

    @Schema(description = "是否超员")
    @ExcelProperty(value = "是否超员", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isOverQuota;

    @Schema(description = "预警级别")
    @ExcelProperty(value = "预警级别", converter = DictConvert.class)
    @DictFormat("safe_report_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer alertLevel;

    @Schema(description = "预警消息")
    @ExcelProperty("预警消息")
    private String alertMessage;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private String recordTime;

    @Schema(description = "记录图片")
    @ExcelProperty(value = "记录图片", converter = ImagePathConverter.class)
    private String recordPic;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
