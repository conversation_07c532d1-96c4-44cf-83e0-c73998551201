package com.nj9394.module.safe.controller.admin.alarmsetting.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 报警配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AlarmSettingRespVO {

    @Schema(description = "报警设置序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11288")
    @ExcelProperty("报警设置序号")
    private Long id;

    @Schema(description = "报警类型（如超员、未佩戴安全帽等）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "报警类型（如超员、未佩戴安全帽等）", converter = DictConvert.class)
    @DictFormat("safe_alarm_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String alarmType;

    @Schema(description = "报警预案编号", example = "16817")
    @ExcelProperty("报警预案编号")
    private Long planId;

    @ExcelProperty("关联预案名称")
    private String planName;

    @ExcelProperty("关联预案路径")
    private String planUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}