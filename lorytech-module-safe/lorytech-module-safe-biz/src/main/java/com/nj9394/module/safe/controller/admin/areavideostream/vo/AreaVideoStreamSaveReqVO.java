package com.nj9394.module.safe.controller.admin.areavideostream.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 视频区域划分新增/修改 Request VO")
@Data
public class AreaVideoStreamSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14287")
    private Long id;

    @Schema(description = "区域ID", example = "13670")
    private Long areaId;

    @Schema(description = "视频流ID", example = "31949")
    private Long videoId;

    @Schema(description = "视频区域")
    private String videoArea;

}