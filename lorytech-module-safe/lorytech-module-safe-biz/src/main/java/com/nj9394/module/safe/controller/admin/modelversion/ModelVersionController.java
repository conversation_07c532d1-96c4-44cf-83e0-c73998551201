package com.nj9394.module.safe.controller.admin.modelversion;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.framework.excel.core.util.ExcelUtils;
import com.nj9394.module.safe.controller.admin.modelversion.vo.ModelVersionPageReqVO;
import com.nj9394.module.safe.controller.admin.modelversion.vo.ModelVersionRespVO;
import com.nj9394.module.safe.controller.admin.modelversion.vo.ModelVersionSaveReqVO;
import com.nj9394.module.safe.dal.dataobject.modelversion.ModelVersionDO;
import com.nj9394.module.safe.service.modelversion.ModelVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nj9394.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 模型版本")
@RestController
@RequestMapping("/safe/model-version")
@Validated
public class ModelVersionController {

    @Resource
    private ModelVersionService modelVersionService;

    @PostMapping("/create")
    @Operation(summary = "创建模型版本")
    @PreAuthorize("@ss.hasPermission('safe:model-version:create')")
    public CommonResult<Long> createModelVersion(@Valid @RequestBody ModelVersionSaveReqVO createReqVO) {
        return success(modelVersionService.createModelVersion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新模型版本")
    @PreAuthorize("@ss.hasPermission('safe:model-version:update')")
    public CommonResult<Boolean> updateModelVersion(@Valid @RequestBody ModelVersionSaveReqVO updateReqVO) {
        modelVersionService.updateModelVersion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除模型版本")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:model-version:delete')")
    public CommonResult<Boolean> deleteModelVersion(@RequestParam("id") Long id) {
        modelVersionService.deleteModelVersion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得模型版本")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:model-version:query')")
    public CommonResult<ModelVersionRespVO> getModelVersion(@RequestParam("id") Long id) {
        ModelVersionDO modelVersion = modelVersionService.getModelVersion(id);
        return success(BeanUtils.toBean(modelVersion, ModelVersionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得模型版本分页")
    @PreAuthorize("@ss.hasPermission('safe:model-version:query')")
    public CommonResult<PageResult<ModelVersionRespVO>> getModelVersionPage(@Valid ModelVersionPageReqVO pageReqVO) {
        PageResult<ModelVersionDO> pageResult = modelVersionService.getModelVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelVersionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出模型版本 Excel")
    @PreAuthorize("@ss.hasPermission('safe:model-version:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportModelVersionExcel(@Valid ModelVersionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ModelVersionDO> list = modelVersionService.getModelVersionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "模型版本.xls", "数据", ModelVersionRespVO.class,
                        BeanUtils.toBean(list, ModelVersionRespVO.class));
    }

}