package com.nj9394.module.safe.controller.admin.areadivision.vo;

import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 区域划分 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AreaDivisionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22570")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "厂房编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7447")
    @ExcelProperty("厂房编号")
    private Long factoryId;

    private String factoryName;

    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("区域名称")
    private String name;

    @Schema(description = "最大允许人数")
    @ExcelProperty("最大允许人数")
    private Integer maxAllowance;

    @Schema(description = "危险等级")
    @ExcelProperty(value = "危险等级", converter = DictConvert.class)
    @DictFormat("safe_report_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String dangerLevel;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "区域")
    @ExcelProperty("区域")
    private String area;

    @Schema(description = "画布图片")
    @ExcelProperty("画布图片")
    private String areaImgPath;

    @Schema(description = "厂房")
    private FactoryInfoRespVO factory;

    @Schema(description = "最大超限人数")
    @ExcelProperty("最大超限人数")
    private Integer maxLimit;

    @Schema(description = "严重超限人数")
    @ExcelProperty("严重超限人数")
    private Integer maxSerious;

}
