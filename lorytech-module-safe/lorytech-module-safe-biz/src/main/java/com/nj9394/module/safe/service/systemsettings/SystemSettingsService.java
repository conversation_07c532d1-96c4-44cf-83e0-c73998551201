package com.nj9394.module.safe.service.systemsettings;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.systemsettings.vo.*;
import com.nj9394.module.safe.dal.dataobject.systemsettings.SystemSettingsDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * SAFE 安全系统设置 Service 接口
 *
 * <AUTHOR>
 */
public interface SystemSettingsService {

    /**
     * 创建SAFE 安全系统设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSystemSettings(@Valid SystemSettingsSaveReqVO createReqVO);

    /**
     * 更新SAFE 安全系统设置
     *
     * @param updateReqVO 更新信息
     */
    void updateSystemSettings(@Valid SystemSettingsSaveReqVO updateReqVO);

    /**
     * 删除SAFE 安全系统设置
     *
     * @param id 编号
     */
    void deleteSystemSettings(Long id);

    /**
     * 获得SAFE 安全系统设置
     *
     * @param id 编号
     * @return SAFE 安全系统设置
     */
    SystemSettingsDO getSystemSettings(Long id);

    /**
     * 获得SAFE 安全系统设置分页
     *
     * @param pageReqVO 分页查询
     * @return SAFE 安全系统设置分页
     */
    PageResult<SystemSettingsDO> getSystemSettingsPage(SystemSettingsPageReqVO pageReqVO);

}