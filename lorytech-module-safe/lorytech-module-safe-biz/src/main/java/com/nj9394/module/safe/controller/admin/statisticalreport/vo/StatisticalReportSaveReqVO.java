package com.nj9394.module.safe.controller.admin.statisticalreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 统计报表新增/修改 Request VO")
@Data
public class StatisticalReportSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2082")
    private Long id;

    @Schema(description = "报表类型", example = "2")
    private String reportType;

    @Schema(description = "报表标题")
    private String reportTitle;

    @Schema(description = "开始时间")
    private LocalDateTime reportStime;

    @Schema(description = "结束时间")
    private LocalDateTime reportEtime;

    @Schema(description = "报表文件路径")
    private String reportFile;

}