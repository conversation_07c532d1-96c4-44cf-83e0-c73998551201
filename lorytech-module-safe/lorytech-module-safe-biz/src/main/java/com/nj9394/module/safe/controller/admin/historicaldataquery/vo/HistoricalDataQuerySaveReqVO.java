package com.nj9394.module.safe.controller.admin.historicaldataquery.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 安全分析数据归档新增/修改 Request VO")
@Data
public class HistoricalDataQuerySaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "27836")
    private Long id;

    @Schema(description = "数据类型", example = "1")
    private String dataType;

    @Schema(description = "区域编号", example = "28892")
    private Long areaId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dataStime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dataEtime;

    @Schema(description = "数据标题")
    private String dataTitle;

    @Schema(description = "文件路径")
    private String dataFile;

}