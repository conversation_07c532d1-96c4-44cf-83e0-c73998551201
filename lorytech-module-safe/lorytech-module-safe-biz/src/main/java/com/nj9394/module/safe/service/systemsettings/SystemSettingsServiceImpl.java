package com.nj9394.module.safe.service.systemsettings;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.nj9394.module.safe.controller.admin.systemsettings.vo.*;
import com.nj9394.module.safe.dal.dataobject.systemsettings.SystemSettingsDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.systemsettings.SystemSettingsMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * SAFE 安全系统设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SystemSettingsServiceImpl implements SystemSettingsService {

    @Resource
    private SystemSettingsMapper systemSettingsMapper;

    @Override
    public Long createSystemSettings(SystemSettingsSaveReqVO createReqVO) {
        // 插入
        SystemSettingsDO systemSettings = BeanUtils.toBean(createReqVO, SystemSettingsDO.class);
        systemSettingsMapper.insert(systemSettings);
        // 返回
        return systemSettings.getId();
    }

    @Override
    public void updateSystemSettings(SystemSettingsSaveReqVO updateReqVO) {
        // 校验存在
        validateSystemSettingsExists(updateReqVO.getId());
        // 更新
        SystemSettingsDO updateObj = BeanUtils.toBean(updateReqVO, SystemSettingsDO.class);
        systemSettingsMapper.updateById(updateObj);
    }

    @Override
    public void deleteSystemSettings(Long id) {
        // 校验存在
        validateSystemSettingsExists(id);
        // 删除
        systemSettingsMapper.deleteById(id);
    }

    private void validateSystemSettingsExists(Long id) {
        if (systemSettingsMapper.selectById(id) == null) {
            throw exception(SYSTEM_SETTINGS_NOT_EXISTS);
        }
    }

    @Override
    public SystemSettingsDO getSystemSettings(Long id) {
        return systemSettingsMapper.selectById(id);
    }

    @Override
    public PageResult<SystemSettingsDO> getSystemSettingsPage(SystemSettingsPageReqVO pageReqVO) {
        return systemSettingsMapper.selectPage(pageReqVO);
    }

}