package com.nj9394.module.safe.controller.admin.modelversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 模型版本新增/修改 Request VO")
@Data
public class ModelVersionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15234")
    private Long id;

    @Schema(description = "模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "模型名称不能为空")
    private String name;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "版本号不能为空")
    private String version;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String addr;

}