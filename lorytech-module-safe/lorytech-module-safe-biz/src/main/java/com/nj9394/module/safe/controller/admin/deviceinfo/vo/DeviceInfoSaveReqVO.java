package com.nj9394.module.safe.controller.admin.deviceinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 设备信息新增/修改 Request VO")
@Data
public class DeviceInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23261")
    private Long id;

    @Schema(description = "设备类型（服务器、LED防爆显示屏等）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "设备类型（服务器、LED防爆显示屏等）不能为空")
    private String deviceType;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "安装位置")
    private String installationLocation;

}