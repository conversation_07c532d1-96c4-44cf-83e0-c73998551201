package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 区域视频列表 Response VO")
@Data
public class QuotaAnalysisVideoListRespVO {
    
    @Schema(description = "区域ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long areaId;
    
    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String areaName;
    
    @Schema(description = "人数上限", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer maxAllowance;
    
    @Schema(description = "视频图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recordPic;
    
    @Schema(description = "实际人数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer currentPersonCount;
    
    @Schema(description = "厂区ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long factoryId;
} 