package com.nj9394.module.safe.controller.admin.videostreaminfo.vo;

import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 视频流信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class VideoStreamInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23123")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "摄像头编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3472")
    @ExcelProperty(value = "摄像头编号", converter = DictConvert.class)
    @DictFormat("safe_report_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Long cameraId;

    @Schema(description = "RTSP地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("RTSP地址")
    private String rtspAddress;

    @Schema(description = "视频质量")
    @ExcelProperty("视频质量")
    private String videoQuality;

    @Schema(description = "关注区域（存储区域的坐标或描述信息）")
    @ExcelProperty("关注区域（存储区域的坐标或描述信息）")
    private String concernedArea;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "摄像头")
    private CameraInfoRespVO camera;

    @Schema(description = "别名")
    @ExcelProperty("别名")
    private String name;

}