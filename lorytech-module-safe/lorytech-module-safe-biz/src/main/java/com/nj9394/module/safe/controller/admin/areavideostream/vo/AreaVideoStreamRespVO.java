package com.nj9394.module.safe.controller.admin.areavideostream.vo;

import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 视频区域划分 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AreaVideoStreamRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14287")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "区域ID", example = "13670")
    @ExcelProperty(value = "区域ID", converter = DictConvert.class)
    @DictFormat("system_user_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Long areaId;

    @Schema(description = "视频流ID", example = "31949")
    @ExcelProperty(value = "视频流ID", converter = DictConvert.class)
    @DictFormat("system_user_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Long videoId;

    @Schema(description = "视频区域")
    @ExcelProperty("视频区域")
    private String videoArea;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "区域划分")
    private AreaDivisionRespVO area;

    @Schema(description = "视频流")
    private VideoStreamInfoRespVO video;

}