package com.nj9394.module.safe.dal.mysql.deviceinfo;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.deviceinfo.DeviceInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.deviceinfo.vo.*;

/**
 * 设备信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceInfoMapper extends BaseMapperX<DeviceInfoDO> {

    default PageResult<DeviceInfoDO> selectPage(DeviceInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceInfoDO>()
                .eqIfPresent(DeviceInfoDO::getDeviceType, reqVO.getDeviceType())
                .eqIfPresent(DeviceInfoDO::getBrand, reqVO.getBrand())
                .eqIfPresent(DeviceInfoDO::getModel, reqVO.getModel())
                .eqIfPresent(DeviceInfoDO::getInstallationLocation, reqVO.getInstallationLocation())
                .betweenIfPresent(DeviceInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceInfoDO::getId));
    }

}