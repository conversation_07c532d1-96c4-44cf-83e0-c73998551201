package com.nj9394.module.safe.controller.admin.alarmsetting.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 报警配置新增/修改 Request VO")
@Data
public class AlarmSettingSaveReqVO {

    @Schema(description = "报警设置序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11288")
    private Long id;

    @Schema(description = "报警类型（如超员、未佩戴安全帽等）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "报警类型（如超员、未佩戴安全帽等）不能为空")
    private String alarmType;

    @Schema(description = "报警预案编号", example = "16817")
    private Long planId;

}