package com.nj9394.module.safe.controller.admin.persondangerinfo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.nj9394.framework.common.util.collection.MapUtils;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisRespVO;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertSet;

import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.persondangerinfo.PersonDangerInfoDO;
import com.nj9394.module.safe.service.persondangerinfo.PersonDangerInfoService;

@Tag(name = "管理后台 - 人员危险情况")
@RestController
@RequestMapping("/safe/person-danger-info")
@Validated
public class PersonDangerInfoController {

    @Resource
    private PersonDangerInfoService personDangerInfoService;

    @Resource
    private AreaDivisionService areaDivisionService;

    @PostMapping("/create")
    @Operation(summary = "创建人员危险情况")
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:create')")
    public CommonResult<Long> createPersonDangerInfo(@Valid @RequestBody PersonDangerInfoSaveReqVO createReqVO) {
        return success(personDangerInfoService.createPersonDangerInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新人员危险情况")
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:update')")
    public CommonResult<Boolean> updatePersonDangerInfo(@Valid @RequestBody PersonDangerInfoSaveReqVO updateReqVO) {
        personDangerInfoService.updatePersonDangerInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除人员危险情况")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:delete')")
    public CommonResult<Boolean> deletePersonDangerInfo(@RequestParam("id") Long id) {
        personDangerInfoService.deletePersonDangerInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得人员危险情况")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:query')")
    public CommonResult<PersonDangerInfoRespVO> getPersonDangerInfo(@RequestParam("id") Long id) {
        PersonDangerInfoDO personDangerInfo = personDangerInfoService.getPersonDangerInfo(id);
        return success(BeanUtils.toBean(personDangerInfo, PersonDangerInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得人员危险情况分页")
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:query')")
    public CommonResult<PageResult<PersonDangerInfoRespVO>> getPersonDangerInfoPage(@Valid PersonDangerInfoPageReqVO pageReqVO) {
        PageResult<PersonDangerInfoDO> pageResult = personDangerInfoService.getPersonDangerInfoPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, PersonDangerInfoRespVO.class));
        return success(buildStockVoPageResult(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出人员危险情况 Excel")
    @PreAuthorize("@ss.hasPermission('safe:person-danger-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPersonDangerInfoExcel(@Valid PersonDangerInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(pageReqVO.getPageSize());
//        List<PersonDangerInfoDO> list = personDangerInfoService.getPersonDangerInfoPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "人员危险情况.xls", "数据", PersonDangerInfoRespVO.class,
//                        BeanUtils.toBean(list, PersonDangerInfoRespVO.class));
        List<PersonDangerInfoRespVO> list = buildStockVoPageResult(personDangerInfoService.getPersonDangerInfoPage(pageReqVO)).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "人员危险情况.xls", "数据", PersonDangerInfoRespVO.class, list);

        //        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("人员危险情况", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        // 使用EasyExcel导出，支持图片嵌入
        EasyExcel.write(response.getOutputStream(), PersonDangerInfoRespVO.class)
                .autoCloseStream(false) // 不要自动关闭，交给Servlet自己处理
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                .sheet("数据")
                .doWrite(list);

    }

    private PageResult<PersonDangerInfoRespVO> buildStockVoPageResult(PageResult<PersonDangerInfoDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), PersonDangerInfoDO::getAreaId));
        return BeanUtils.toBean(pageResult, PersonDangerInfoRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
        });
    }

}