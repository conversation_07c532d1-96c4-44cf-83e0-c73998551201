package com.nj9394.module.safe.controller.admin.systemsettings.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - SAFE 安全系统设置新增/修改 Request VO")
@Data
public class SystemSettingsSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16128")
    private Long id;

    @Schema(description = "设置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "设置名称不能为空")
    private String settingName;

    @Schema(description = "设置值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设置值不能为空")
    private String settingValue;

}