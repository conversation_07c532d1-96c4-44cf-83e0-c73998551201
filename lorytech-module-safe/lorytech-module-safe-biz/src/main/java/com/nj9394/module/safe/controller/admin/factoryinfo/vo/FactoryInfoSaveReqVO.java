package com.nj9394.module.safe.controller.admin.factoryinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 厂房信息新增/修改 Request VO")
@Data
public class FactoryInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24321")
    private Long id;

    @Schema(description = "厂房名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "厂房名称不能为空")
    private String name;

    @Schema(description = "平面图")
    private String floorPlan;

    @Schema(description = "面积")
    private String area;

    @Schema(description = "用途")
    private String purpose;

}