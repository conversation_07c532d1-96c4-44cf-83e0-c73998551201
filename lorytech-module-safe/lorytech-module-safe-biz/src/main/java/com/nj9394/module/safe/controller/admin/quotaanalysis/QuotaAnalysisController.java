package com.nj9394.module.safe.controller.admin.quotaanalysis;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.nj9394.framework.common.util.collection.MapUtils;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertSet;

import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.*;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.service.quotaanalysis.QuotaAnalysisService;

@Tag(name = "管理后台 - 定员分析")
@RestController
@RequestMapping("/safe/quota-analysis")
@Validated
public class QuotaAnalysisController {

    @Resource
    private QuotaAnalysisService quotaAnalysisService;

    @Resource
    private AreaDivisionService areaDivisionService;

    @PostMapping("/create")
    @Operation(summary = "创建定员分析")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:create')")
    public CommonResult<Long> createQuotaAnalysis(@Valid @RequestBody QuotaAnalysisSaveReqVO createReqVO) {
        return success(quotaAnalysisService.createQuotaAnalysis(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新定员分析")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:update')")
    public CommonResult<Boolean> updateQuotaAnalysis(@Valid @RequestBody QuotaAnalysisSaveReqVO updateReqVO) {
        quotaAnalysisService.updateQuotaAnalysis(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除定员分析")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:delete')")
    public CommonResult<Boolean> deleteQuotaAnalysis(@RequestParam("id") Long id) {
        quotaAnalysisService.deleteQuotaAnalysis(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得定员分析")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<QuotaAnalysisRespVO> getQuotaAnalysis(@RequestParam("id") Long id) {
        QuotaAnalysisDO quotaAnalysis = quotaAnalysisService.getQuotaAnalysis(id);
        return success(BeanUtils.toBean(quotaAnalysis, QuotaAnalysisRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得定员分析分页")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<PageResult<QuotaAnalysisRespVO>> getQuotaAnalysisPage(@Valid QuotaAnalysisPageReqVO pageReqVO) {
        PageResult<QuotaAnalysisDO> pageResult = quotaAnalysisService.getQuotaAnalysisPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, QuotaAnalysisRespVO.class));
        return success(buildStockVoPageResult(pageResult));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出定员分析 Excel")
//    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportQuotaAnalysisExcel(@Valid QuotaAnalysisPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
////        List<QuotaAnalysisDO> list = quotaAnalysisService.getQuotaAnalysisPage(pageReqVO).getList();
////        // 导出 Excel
////        ExcelUtils.write(response, "定员分析.xls", "数据", QuotaAnalysisRespVO.class,
////                        BeanUtils.toBean(list, QuotaAnalysisRespVO.class));
//
//        List<QuotaAnalysisRespVO> list = buildStockVoPageResult(quotaAnalysisService.getQuotaAnalysisPage(pageReqVO)).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "定员分析.xls", "数据", QuotaAnalysisRespVO.class, list);
//
//    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出定员分析 Excel")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportQuotaAnalysisExcel(@Valid QuotaAnalysisPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(pageReqVO.getPageSize());
        //List<QuotaAnalysisDO> list = quotaAnalysisService.getQuotaAnalysisPage(pageReqVO).getList();
        // 导出 Excel
//        ExcelUtils.write(response, "定员分析.xls", "数据", QuotaAnalysisRespVO.class,
//                        BeanUtils.toBean(list, QuotaAnalysisRespVO.class));

        List<QuotaAnalysisRespVO> list = buildStockVoPageResult(quotaAnalysisService
                .getQuotaAnalysisPage(pageReqVO)).getList();

//        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("定员分析", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 使用EasyExcel导出，支持图片嵌入
        EasyExcel.write(response.getOutputStream(), QuotaAnalysisRespVO.class)
                .autoCloseStream(false) // 不要自动关闭，交给Servlet自己处理
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                .sheet("数据")
                .doWrite(list);
    }



    private PageResult<QuotaAnalysisRespVO> buildStockVoPageResult(PageResult<QuotaAnalysisDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), QuotaAnalysisDO::getAreaId));
        return BeanUtils.toBean(pageResult, QuotaAnalysisRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
        });
    }
    
    @GetMapping("/factory-stats")
    @Operation(summary = "获取各个厂区的人数统计数据")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<List<QuotaAnalysisFactoryStatsRespVO>> getFactoryPersonStats() {
        List<QuotaAnalysisFactoryStatsRespVO> stats = quotaAnalysisService.getFactoryPersonStats();
        return success(stats);
    }
    
    @GetMapping("/total-person-count")
    @Operation(summary = "获取总人数")
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<Integer> getTotalPersonCount() {
        int totalCount = quotaAnalysisService.getTotalPersonCount();
        return success(totalCount);
    }
    
    @GetMapping("/video-list")
    @Operation(summary = "获取视频列表数据")
    @Parameter(name = "factoryId", description = "厂区ID，不传则查询所有厂区", required = false)
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<List<QuotaAnalysisVideoListRespVO>> getVideoList(@RequestParam(value = "factoryId", required = false) Long factoryId) {
        List<QuotaAnalysisVideoListRespVO> videoList = quotaAnalysisService.getVideoList(factoryId);
        return success(videoList);
    }

    @GetMapping("/history-person-count")
    @Operation(summary = "获取历史人数数据")
    @Parameter(name = "factoryId", description = "厂区ID，不传则查询所有厂区", required = false)
    @Parameter(name = "days", description = "查询天数，默认30天", required = false)
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<Map<String, Object>> getHistoryPersonCount(
            @RequestParam(value = "factoryId", required = false) Long factoryId,
            @RequestParam(value = "days", required = false, defaultValue = "30") Integer days) {
        return success(quotaAnalysisService.getHistoryPersonCount(factoryId, days));
    }
    
    @GetMapping("/factory-flow-rate")
    @Operation(summary = "获取厂区人流量数据")
    @Parameter(name = "factoryId", description = "厂区ID，不传则查询所有厂区", required = false)
    @PreAuthorize("@ss.hasPermission('safe:quota-analysis:query')")
    public CommonResult<QuotaAnalysisFlowRateRespVO> getFactoryFlowRate(
            @RequestParam(value = "factoryId", required = false) Long factoryId) {
        return success(quotaAnalysisService.getFactoryFlowRate(factoryId));
    }
}