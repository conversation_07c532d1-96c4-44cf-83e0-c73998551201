package com.nj9394.module.safe.controller.admin.statisticalreport;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.statisticalreport.vo.*;
import com.nj9394.module.safe.dal.dataobject.statisticalreport.StatisticalReportDO;
import com.nj9394.module.safe.service.statisticalreport.StatisticalReportService;

@Tag(name = "管理后台 - 统计报表")
@RestController
@RequestMapping("/safe/statistical-report")
@Validated
public class StatisticalReportController {

    @Resource
    private StatisticalReportService statisticalReportService;

    @PostMapping("/create")
    @Operation(summary = "创建统计报表")
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:create')")
    public CommonResult<Long> createStatisticalReport(@Valid @RequestBody StatisticalReportSaveReqVO createReqVO) {
        return success(statisticalReportService.createStatisticalReport(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新统计报表")
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:update')")
    public CommonResult<Boolean> updateStatisticalReport(@Valid @RequestBody StatisticalReportSaveReqVO updateReqVO) {
        statisticalReportService.updateStatisticalReport(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除统计报表")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:delete')")
    public CommonResult<Boolean> deleteStatisticalReport(@RequestParam("id") Long id) {
        statisticalReportService.deleteStatisticalReport(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得统计报表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:query')")
    public CommonResult<StatisticalReportRespVO> getStatisticalReport(@RequestParam("id") Long id) {
        StatisticalReportDO statisticalReport = statisticalReportService.getStatisticalReport(id);
        return success(BeanUtils.toBean(statisticalReport, StatisticalReportRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得统计报表分页")
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:query')")
    public CommonResult<PageResult<StatisticalReportRespVO>> getStatisticalReportPage(@Valid StatisticalReportPageReqVO pageReqVO) {
        PageResult<StatisticalReportDO> pageResult = statisticalReportService.getStatisticalReportPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StatisticalReportRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出统计报表 Excel")
    @PreAuthorize("@ss.hasPermission('safe:statistical-report:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStatisticalReportExcel(@Valid StatisticalReportPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StatisticalReportDO> list = statisticalReportService.getStatisticalReportPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "统计报表.xls", "数据", StatisticalReportRespVO.class,
                        BeanUtils.toBean(list, StatisticalReportRespVO.class));
    }

}