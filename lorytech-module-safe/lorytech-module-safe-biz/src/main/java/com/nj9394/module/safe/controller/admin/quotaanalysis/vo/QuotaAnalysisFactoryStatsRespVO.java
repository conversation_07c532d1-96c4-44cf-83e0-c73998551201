package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 厂区人数统计数据 Response VO
 */
@Schema(description = "管理后台 - 厂区人数统计数据 Response VO")
@Data
public class QuotaAnalysisFactoryStatsRespVO {

    @Schema(description = "厂区ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long factoryId;

    @Schema(description = "厂区名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String factoryName;

    @Schema(description = "当前人数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer personCount;

    @Schema(description = "百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double percentage;
} 