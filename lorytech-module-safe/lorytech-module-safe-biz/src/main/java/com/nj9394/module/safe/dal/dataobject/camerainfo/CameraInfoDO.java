package com.nj9394.module.safe.dal.dataobject.camerainfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.nj9394.framework.mybatis.core.dataobject.BaseDO;

/**
 * 摄像头信息 DO
 *
 * <AUTHOR>
 */
@TableName("safe_camera_info")
@KeySequence("safe_camera_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CameraInfoDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 厂房编号
     */
    private Long factoryId;
    /**
     * 摄像头编号
     */
    private String number;
    /**
     * 摄像头类型
     *
     * 枚举 {@link TODO safe_report_type 对应的类}
     */
    private String type;
    /**
     * 安装位置
     */
    private String installationLocation;
    /**
     * 覆盖范围
     */
    private String coverageRange;
    /**
     * 工作状态（在线、离线等）
     *
     * 枚举 {@link TODO safe_report_type 对应的类}
     */
    private String status;

}