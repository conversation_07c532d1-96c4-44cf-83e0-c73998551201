package com.nj9394.module.safe.controller.admin.historicaldataquery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 安全分析数据归档 Response VO")
@Data
@ExcelIgnoreUnannotated
public class HistoricalDataQueryRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "27836")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty(value = "数据类型", converter = DictConvert.class)
    @DictFormat("safe_data_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String dataType;

    @Schema(description = "区域编号", example = "28892")
    @ExcelProperty("区域编号")
    private Long areaId;

    private String areaName;

    private String factoryName;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime dataStime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime dataEtime;

    @Schema(description = "数据标题")
    @ExcelProperty("数据标题")
    private String dataTitle;

    @Schema(description = "数据文件地址")
    @ExcelProperty("数据文件地址")
    private String dataFile;

}