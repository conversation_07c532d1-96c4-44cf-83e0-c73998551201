package com.nj9394.module.safe.controller.admin.factoryinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 厂房信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FactoryInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24321")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "厂房名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("厂房名称")
    private String name;

    @Schema(description = "平面图")
    @ExcelProperty("平面图")
    private String floorPlan;

    @Schema(description = "面积")
    @ExcelProperty("面积")
    private String area;

    @Schema(description = "用途")
    @ExcelProperty("用途")
    private String purpose;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}