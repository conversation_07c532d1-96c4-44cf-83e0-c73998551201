package com.nj9394.module.safe.controller.admin.systemsettings.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - SAFE 安全系统设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SystemSettingsRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16128")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "设置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("设置名称")
    private String settingName;

    @Schema(description = "设置值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设置值")
    private String settingValue;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}