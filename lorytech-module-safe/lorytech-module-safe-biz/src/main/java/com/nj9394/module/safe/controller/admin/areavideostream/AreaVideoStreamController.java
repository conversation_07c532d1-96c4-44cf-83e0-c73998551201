package com.nj9394.module.safe.controller.admin.areavideostream;

import cn.hutool.core.collection.CollectionUtil;
import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.framework.excel.core.util.ExcelUtils;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionPageReqVO;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.areavideostream.vo.AreaVideoStreamPageReqVO;
import com.nj9394.module.safe.controller.admin.areavideostream.vo.AreaVideoStreamRespVO;
import com.nj9394.module.safe.controller.admin.areavideostream.vo.AreaVideoStreamSaveReqVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoRespVO;
import com.nj9394.module.safe.dal.dataobject.areadivision.AreaDivisionDO;
import com.nj9394.module.safe.dal.dataobject.areavideostream.AreaVideoStreamDO;
import com.nj9394.module.safe.dal.dataobject.videostreaminfo.VideoStreamInfoDO;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import com.nj9394.module.safe.service.areavideostream.AreaVideoStreamService;
import com.nj9394.module.safe.service.videostreaminfo.VideoStreamInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nj9394.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 视频区域划分")
@RestController
@RequestMapping("/safe/area-video-stream")
@Validated
public class AreaVideoStreamController {

    @Resource
    private AreaVideoStreamService areaVideoStreamService;

    @Resource
    private AreaDivisionService areaDivisionService;

    @Resource
    private VideoStreamInfoService videoStreamInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建视频区域划分")
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:create')")
    public CommonResult<Long> createAreaVideoStream(@Valid @RequestBody AreaVideoStreamSaveReqVO createReqVO) {
        return success(areaVideoStreamService.createAreaVideoStream(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新视频区域划分")
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:update')")
    public CommonResult<Boolean> updateAreaVideoStream(@Valid @RequestBody AreaVideoStreamSaveReqVO updateReqVO) {
        areaVideoStreamService.updateAreaVideoStream(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除视频区域划分")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:delete')")
    public CommonResult<Boolean> deleteAreaVideoStream(@RequestParam("id") Long id) {
        areaVideoStreamService.deleteAreaVideoStream(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得视频区域划分")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:query')")
    public CommonResult<AreaVideoStreamRespVO> getAreaVideoStream(@RequestParam("id") Long id) {
        AreaVideoStreamDO areaVideoStream = areaVideoStreamService.getAreaVideoStream(id);
        AreaVideoStreamRespVO bean = BeanUtils.toBean(areaVideoStream, AreaVideoStreamRespVO.class);
        bean.setArea(BeanUtils.toBean(areaDivisionService.getAreaDivision(bean.getAreaId()), AreaDivisionRespVO.class));
        bean.setVideo(BeanUtils.toBean(videoStreamInfoService.getVideoStreamInfo(bean.getVideoId()), VideoStreamInfoRespVO.class));
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得视频区域划分分页")
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:query')")
    public CommonResult<PageResult<AreaVideoStreamRespVO>> getAreaVideoStreamPage(@Valid AreaVideoStreamPageReqVO pageReqVO) {
        PageResult<AreaVideoStreamDO> pageResult = areaVideoStreamService.getAreaVideoStreamPage(pageReqVO);
        PageResult<AreaVideoStreamRespVO> page = BeanUtils.toBean(pageResult, AreaVideoStreamRespVO.class);

        AreaDivisionPageReqVO areaDivisionPageReqVO = new AreaDivisionPageReqVO();
        areaDivisionPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);

        VideoStreamInfoPageReqVO videoStreamInfoPageReqVO = new VideoStreamInfoPageReqVO();
        videoStreamInfoPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (!CollectionUtil.isEmpty(page.getList())) {
            List<AreaDivisionDO> list1 = areaDivisionService.getAreaDivisionPage(areaDivisionPageReqVO).getList();
            List<VideoStreamInfoDO> list2 = videoStreamInfoService.getVideoStreamInfoPage(videoStreamInfoPageReqVO).getList();

            Map<Long, AreaDivisionDO> map1 = list1.stream().collect(Collectors.toMap(AreaDivisionDO::getId, Function.identity()));
            Map<Long, VideoStreamInfoDO> map2 = list2.stream().collect(Collectors.toMap(VideoStreamInfoDO::getId, Function.identity()));

            page.getList().stream().forEach(item -> {
                AreaDivisionDO area = map1.get(item.getAreaId());
                VideoStreamInfoDO video = map2.get(item.getVideoId());
                item.setArea(area == null ? null : BeanUtils.toBean(area, AreaDivisionRespVO.class));
                item.setVideo(video == null ? null : BeanUtils.toBean(video, VideoStreamInfoRespVO.class));
            });
        }

        return success(page);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出视频区域划分 Excel")
    @PreAuthorize("@ss.hasPermission('safe:area-video-stream:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAreaVideoStreamExcel(@Valid AreaVideoStreamPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AreaVideoStreamDO> list = areaVideoStreamService.getAreaVideoStreamPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "视频区域划分.xls", "数据", AreaVideoStreamRespVO.class,
                        BeanUtils.toBean(list, AreaVideoStreamRespVO.class));
    }

}