package com.nj9394.module.safe.dal.mysql.datastorage;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.datastorage.DataStorageDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.datastorage.vo.*;

/**
 * 数据存储 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataStorageMapper extends BaseMapperX<DataStorageDO> {

    default PageResult<DataStorageDO> selectPage(DataStoragePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DataStorageDO>()
                .eqIfPresent(DataStorageDO::getMonitoringData, reqVO.getMonitoringData())
                .betweenIfPresent(DataStorageDO::getBackupTime, reqVO.getBackupTime())
                .betweenIfPresent(DataStorageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DataStorageDO::getId));
    }

}