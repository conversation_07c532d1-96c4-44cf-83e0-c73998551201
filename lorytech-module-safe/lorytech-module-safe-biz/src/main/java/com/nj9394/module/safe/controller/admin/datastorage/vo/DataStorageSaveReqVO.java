package com.nj9394.module.safe.controller.admin.datastorage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 数据存储新增/修改 Request VO")
@Data
public class DataStorageSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29314")
    private Long id;

    @Schema(description = "监控录像及分析数据（基于文件路径或二进制存储）")
    private byte[] monitoringData;

    @Schema(description = "备份时间")
    private LocalDateTime backupTime;

}