package com.nj9394.module.safe.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nj9394.module.safe.service.python.PythonBatchService;
import com.nj9394.module.safe.service.videostreaminfo.VideoStreamInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
public class InitProjectApp {

    private static final Logger logger = LoggerFactory.getLogger(InitProjectApp.class);

    @Resource
    private VideoStreamInfoService videoStreamInfoService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private PythonService pythonService;

    @Resource
    private ObjectMapper objectMapper;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        logger.info("开始初始化安全帽检测服务...");
        try {
            // 获取所有工作中的视频流信息
            List<Map<String, Object>> streamList = videoStreamInfoService.getWorkingVideoStreamWithAreaList();
            logger.info("获取到 {} 个工作中的视频流", streamList.size());

            // 创建线程池
            executorService = Executors.newFixedThreadPool(streamList.size());

            // 为每个视频流启动一个线程
            for (Map<String, Object> stream : streamList) {
                executorService.submit(() -> {
                    try {
                        Long id = getLongValue(stream, "id");
                        Long cameraId = getLongValue(stream, "camera_id");
                        String rtspAddress = getStringValue(stream, "rtsp_address");

                        if (rtspAddress != null && !rtspAddress.isEmpty()) {
                            logger.info("启动视频流处理: ID={}, CameraID={}, RTSP={}", id, cameraId, rtspAddress);
                            pythonService.processVideoStream(stream, rtspAddress);
                        } else {
                            logger.warn("视频流地址为空: ID={}, CameraID={}", id, cameraId);
                        }
                    } catch (Exception e) {
                        logger.error("处理视频流时发生错误", e);
                    }
                });
            }
        } catch (Exception e) {
            logger.error("初始化安全帽检测服务失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        logger.info("开始关闭安全帽检测服务...");

        // 关闭所有Python进程
        try {
            pythonService.shutdownAllProcesses();
        } catch (Exception e) {
            logger.error("关闭Python进程时出错", e);
        }

        // 关闭线程池
        if (executorService != null) {
            try {
                executorService.shutdown();
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
                logger.info("线程池已关闭");
            } catch (Exception e) {
                logger.error("关闭线程池时出错", e);
                executorService.shutdownNow();
            }
        }

        logger.info("安全帽检测服务已关闭");
    }

    // 辅助方法，从Map中获取Long类型的值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法，从Map中获取String类型的值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
}
