package com.nj9394.module.safe.controller.admin.environmentdangerinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 危险环境分析新增/修改 Request VO")
@Data
public class EnvironmentDangerInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20046")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "26091")
    @NotNull(message = "区域编号不能为空")
    private Long areaId;

    @Schema(description = "是否有火情")
    private Integer isFire;

    @Schema(description = "是否有爆燃")
    private Integer isExplosion;

    @Schema(description = "记录时间")
    private LocalDateTime recordTime;

}
