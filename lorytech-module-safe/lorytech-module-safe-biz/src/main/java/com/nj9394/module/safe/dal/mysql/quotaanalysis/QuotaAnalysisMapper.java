package com.nj9394.module.safe.dal.mysql.quotaanalysis;

import java.time.LocalDateTime;
import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 定员分析 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuotaAnalysisMapper extends BaseMapperX<QuotaAnalysisDO> {

    default PageResult<QuotaAnalysisDO> selectPage(QuotaAnalysisPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuotaAnalysisDO>()
                .eqIfPresent(QuotaAnalysisDO::getAreaId, reqVO.getAreaId())
                .eqIfPresent(QuotaAnalysisDO::getIsOverQuota, reqVO.getIsOverQuota())
                .eqIfPresent(QuotaAnalysisDO::getAlertLevel, reqVO.getAlertLevel())
                .betweenIfPresent(QuotaAnalysisDO::getRecordTime, reqVO.getRecordTime())
                .betweenIfPresent(QuotaAnalysisDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuotaAnalysisDO::getId));
    }

    /**
     * 获取指定时间定员分析次数
     * @param stime
     * @param etime
     * @return
     */
    @Select("SELECT area_id, COUNT(id) as count FROM safe_quota_analysis WHERE deleted=0 AND record_time BETWEEN #{stime} AND #{etime} GROUP BY area_id")
    List<Map<String, Object>> selectCountList(@Param("stime") String stime, @Param("etime") String etime);

    /**
     * 获取各个区域指定时间段定员分析超员数量
     * @param stime
     * @param etime
     * @return
     */
    @Select("SELECT area_id, alert_level, COUNT(id) as count FROM safe_quota_analysis WHERE deleted=0 AND is_over_quota=1 AND " +
            "record_time BETWEEN #{stime} AND #{etime} GROUP BY area_id, alert_level")
    List<Map<String, Object>> selectAlarmCountList(@Param("stime") String stime, @Param("etime") String etime);

    /**
     * 获取每个工厂的最新人数统计
     *
     * @param date 日期，格式为 yyyy-MM-dd
     * @return 工厂人数统计列表
     */
    @Select("SELECT f.id AS factory_id, f.name AS factory_name, " +
            "SUM(q.current_person_count) AS person_count " +
            "FROM safe_quota_analysis q " +
            "JOIN safe_area_division a ON q.area_id = a.id " +
            "JOIN safe_factory_info f ON a.factory_id = f.id " +
            "WHERE DATE(q.record_time) = #{date} " +
            "AND q.id IN (" +
            "  SELECT MAX(id) FROM safe_quota_analysis " +
            "  WHERE DATE(record_time) = #{date} " +
            "  GROUP BY area_id" +
            ") " +
            "GROUP BY f.id, f.name")
    List<Map<String, Object>> selectFactoryPersonStats(@Param("date") String date);

    /**
     * 获取指定日期的总人数
     *
     * @param date 日期，格式为 yyyy-MM-dd
     * @return 总人数
     */
    @Select("SELECT SUM(q.current_person_count) AS total_count " +
            "FROM safe_quota_analysis q " +
            "WHERE DATE(q.record_time) = #{date} " +
            "AND q.id IN (" +
            "  SELECT MAX(id) FROM safe_quota_analysis " +
            "  WHERE DATE(record_time) = #{date} " +
            "  GROUP BY area_id" +
            ")")
    Integer selectTotalPersonCount(@Param("date") String date);

    /**
     * 获取当日各区域的最新视频数据
     *
     * @param date 日期，格式为 yyyy-MM-dd
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @return 视频列表数据
     */
    @Select("<script>" +
            "SELECT q.area_id, a.name AS area_name, a.max_allowance, q.record_pic, q.current_person_count, a.factory_id " +
            "FROM safe_quota_analysis q " +
            "JOIN safe_area_division a ON q.area_id = a.id " +
            "WHERE DATE(q.record_time) = #{date} " +
            "AND q.id IN (" +
            "  SELECT MAX(id) FROM safe_quota_analysis " +
            "  WHERE DATE(record_time) = #{date} " +
            "  GROUP BY area_id" +
            ") " +
            "<if test='factoryId != null'>" +
            "  AND a.factory_id = #{factoryId}" +
            "</if>" +
            "ORDER BY a.factory_id, a.id" +
            "</script>")
    List<Map<String, Object>> selectVideoList(@Param("date") String date, @Param("factoryId") Long factoryId);

    /**
     * 获取历史人数数据
     *
     * @param startTime 开始时间
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @return 历史人数数据
     */
    @Select("<script>" +
            "SELECT q.area_id AS areaId, a.name AS areaName, " +
            "DATE_FORMAT(q.record_time, '%Y-%m-%d %H:%i:%s') AS recordTime, " +
            "q.current_person_count AS personCount, a.factory_id AS factoryId " +
            "FROM safe_quota_analysis q " +
            "JOIN safe_area_division a ON q.area_id = a.id " +
            "WHERE q.record_time >= #{startTime} " +
            "<if test='factoryId != null'>" +
            "  AND a.factory_id = #{factoryId}" +
            "</if>" +
            "ORDER BY a.factory_id, q.area_id, q.record_time" +
            "</script>")
    List<QuotaAnalysisHistoryDataVO> selectHistoryPersonCount(@Param("startTime") LocalDateTime startTime, @Param("factoryId") Long factoryId);

    /**
     * 查询厂区人流量数据
     *
     * @param factoryId 厂区ID，如果为null则查询所有厂区
     * @return 厂区人流量数据
     */
    Map<String, Object> selectFactoryFlowRate(@Param("factoryId") Long factoryId);

}
