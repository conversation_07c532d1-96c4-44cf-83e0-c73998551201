package com.nj9394.module.safe.controller.admin.environmentdangerinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.environmentdangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.environmentdangerinfo.EnvironmentDangerInfoDO;
import com.nj9394.module.safe.service.environmentdangerinfo.EnvironmentDangerInfoService;

@Tag(name = "管理后台 - 危险环境分析")
@RestController
@RequestMapping("/safe/environment-danger-info")
@Validated
public class EnvironmentDangerInfoController {

    @Resource
    private EnvironmentDangerInfoService environmentDangerInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建危险环境分析")
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:create')")
    public CommonResult<Long> createEnvironmentDangerInfo(@Valid @RequestBody EnvironmentDangerInfoSaveReqVO createReqVO) {
        return success(environmentDangerInfoService.createEnvironmentDangerInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新危险环境分析")
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:update')")
    public CommonResult<Boolean> updateEnvironmentDangerInfo(@Valid @RequestBody EnvironmentDangerInfoSaveReqVO updateReqVO) {
        environmentDangerInfoService.updateEnvironmentDangerInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除危险环境分析")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:delete')")
    public CommonResult<Boolean> deleteEnvironmentDangerInfo(@RequestParam("id") Long id) {
        environmentDangerInfoService.deleteEnvironmentDangerInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得危险环境分析")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:query')")
    public CommonResult<EnvironmentDangerInfoRespVO> getEnvironmentDangerInfo(@RequestParam("id") Long id) {
        EnvironmentDangerInfoDO environmentDangerInfo = environmentDangerInfoService.getEnvironmentDangerInfo(id);
        return success(BeanUtils.toBean(environmentDangerInfo, EnvironmentDangerInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得危险环境分析分页")
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:query')")
    public CommonResult<PageResult<EnvironmentDangerInfoRespVO>> getEnvironmentDangerInfoPage(@Valid EnvironmentDangerInfoPageReqVO pageReqVO) {
        PageResult<EnvironmentDangerInfoDO> pageResult = environmentDangerInfoService.getEnvironmentDangerInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EnvironmentDangerInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出危险环境分析 Excel")
    @PreAuthorize("@ss.hasPermission('safe:environment-danger-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEnvironmentDangerInfoExcel(@Valid EnvironmentDangerInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EnvironmentDangerInfoDO> list = environmentDangerInfoService.getEnvironmentDangerInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "危险环境分析.xls", "数据", EnvironmentDangerInfoRespVO.class,
                        BeanUtils.toBean(list, EnvironmentDangerInfoRespVO.class));
    }

}