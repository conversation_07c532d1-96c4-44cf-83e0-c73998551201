package com.nj9394.module.safe.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;


import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;

public class ImagePathConverter implements Converter<String>{

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws IOException {
        // 获取图片路径
        String imagePath = context.getValue();
        // 空路径快速返回
        if (StringUtils.isEmpty(imagePath)) {
            return new WriteCellData<>("");
        }

        try {
            byte[] imageBytes = loadImageBytes(imagePath); // 统一加载图片
            if (imageBytes == null || imageBytes.length == 0) {
                return new WriteCellData<>("[图片加载失败]");
            }

            // 自动检测图片类型（支持PNG/JPEG）
            ImageData.ImageType imageType = detectImageType(imagePath, imageBytes);
            // 创建图片数据（默认尺寸100x100）
            ImageData imageData = new ImageData();
            imageData.setImage(imageBytes);          // 图片字节数组
            imageData.setImageType(imageType);       // 图片类型（如 ImageType.PICTURE_TYPE_PNG）
            //imageData.setImageName("EmbeddedImage"); // 图片名称（可选）

            // 控制图片位置（覆盖单元格范围）
            imageData.setRelativeFirstRowIndex(0);   // 起始行偏移（0=当前行）
            imageData.setRelativeFirstColumnIndex(0); // 起始列偏移（0=当前列）
            imageData.setRelativeLastRowIndex(1);     // 结束行偏移（1=跨1行）
            imageData.setRelativeLastColumnIndex(1); // 结束列偏移（1=跨1列）
            // 设置边距（单位：像素）
            imageData.setTop(5);
            imageData.setRight(5);
            imageData.setBottom(5);
            imageData.setLeft(5);

            // 返回带图片的单元格 - 修改这部分代码
            WriteCellData<String> cellData = new WriteCellData<>("");

            // 创建图片数据列表并添加图片数据
            List<ImageData> imageDataList = new ArrayList<>();
            imageDataList.add(imageData);

            // 使用setImageDataList替代setImageValue
            cellData.setImageDataList(imageDataList);

            return cellData;
        } catch (Exception e) {
            return new WriteCellData<>("[系统错误: " + e.getMessage() + "]");
        }
    }

    // 统一加载图片字节流
    private byte[] loadImageBytes(String imagePath) throws IOException {
        if (imagePath.startsWith("http")) {
            return downloadNetworkImage(imagePath); // 网络图片下载
        } else {
            return readLocalImage(imagePath); // 本地文件读取
        }
    }

    // 下载网络图片（带超时控制）
    private byte[] downloadNetworkImage(String url) throws IOException {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setConfig(RequestConfig.custom()
                    .setConnectTimeout(5000) // 5秒连接超时
                    .setSocketTimeout(10000) // 10秒读取超时
                    .build());

            try (CloseableHttpResponse response = client.execute(request);
                 InputStream is = response.getEntity().getContent()) {
                return IOUtils.toByteArray(is);
            }
        }
    }

    // 读取本地图片（自动压缩）
    private byte[] readLocalImage(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            return null;
        }

        // 图片压缩（大于1MB时缩放50%）
        byte[] original = FileUtils.readFileToByteArray(file);
        if (original.length > 1024 * 1024) {
            return compressImage(original);
        }
        return original;
    }

    // 图片压缩（防止内存溢出）
    private byte[] compressImage(byte[] original) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Thumbnails.of(new ByteArrayInputStream(original))
                .scale(0.5) // 缩放50%
                .outputFormat("png") // 统一转PNG
                .toOutputStream(baos);
        return baos.toByteArray();
    }

    // 自动检测图片类型
    private ImageData.ImageType detectImageType(String path, byte[] bytes) {
        String contentType = URLConnection.guessContentTypeFromName(path);
        if (contentType == null) {
            try {
                contentType = URLConnection.guessContentTypeFromStream(new ByteArrayInputStream(bytes));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return (contentType != null && contentType.contains("png")) ?
                ImageData.ImageType.PICTURE_TYPE_PNG :
                ImageData.ImageType.PICTURE_TYPE_JPEG;
    }
}
