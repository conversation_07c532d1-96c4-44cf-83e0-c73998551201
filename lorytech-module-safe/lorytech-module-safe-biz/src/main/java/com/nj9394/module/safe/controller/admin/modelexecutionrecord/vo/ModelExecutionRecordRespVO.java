package com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 模型执行 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelExecutionRecordRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22496")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14145")
    @ExcelProperty("模型编号")
    private Long modelId;

    @Schema(description = "执行时间")
    @ExcelProperty("执行时间")
    private LocalDateTime executionTime;

    @Schema(description = "处理速度")
    @ExcelProperty("处理速度")
    private String processingSpeed;

    @Schema(description = "识别准确率")
    @ExcelProperty("识别准确率")
    private String recognitionAccuracy;

    @Schema(description = "执行时长")
    @ExcelProperty("执行时长")
    private String executionDuration;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}