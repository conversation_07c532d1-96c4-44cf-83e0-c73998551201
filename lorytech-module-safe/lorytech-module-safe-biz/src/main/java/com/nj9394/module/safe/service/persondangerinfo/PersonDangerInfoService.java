package com.nj9394.module.safe.service.persondangerinfo;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.persondangerinfo.PersonDangerInfoDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * 人员危险情况 Service 接口
 *
 * <AUTHOR>
 */
public interface PersonDangerInfoService {

    /**
     * 创建人员危险情况
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPersonDangerInfo(@Valid PersonDangerInfoSaveReqVO createReqVO);

    /**
     * 更新人员危险情况
     *
     * @param updateReqVO 更新信息
     */
    void updatePersonDangerInfo(@Valid PersonDangerInfoSaveReqVO updateReqVO);

    /**
     * 删除人员危险情况
     *
     * @param id 编号
     */
    void deletePersonDangerInfo(Long id);

    /**
     * 获得人员危险情况
     *
     * @param id 编号
     * @return 人员危险情况
     */
    PersonDangerInfoDO getPersonDangerInfo(Long id);

    /**
     * 获得人员危险情况分页
     *
     * @param pageReqVO 分页查询
     * @return 人员危险情况分页
     */
    PageResult<PersonDangerInfoDO> getPersonDangerInfoPage(PersonDangerInfoPageReqVO pageReqVO);

}