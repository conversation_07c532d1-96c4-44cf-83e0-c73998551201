package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 定员分析新增/修改 Request VO")
@Data
public class QuotaAnalysisSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30802")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6850")
    @NotNull(message = "区域编号不能为空")
    private Long areaId;

    @Schema(description = "当前人员数量", example = "23753")
    private Integer currentPersonCount;

    @Schema(description = "是否超员")
    private Integer isOverQuota;

    @Schema(description = "预警级别")
    private Integer alertLevel;

    @Schema(description = "预警消息")
    private String alertMessage;

    @Schema(description = "记录时间")
    private String recordTime;

    @Schema(description = "记录图片")
    private String recordPic;

}
