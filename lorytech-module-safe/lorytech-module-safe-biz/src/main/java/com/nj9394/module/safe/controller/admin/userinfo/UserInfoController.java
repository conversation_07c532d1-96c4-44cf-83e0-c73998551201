package com.nj9394.module.safe.controller.admin.userinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.userinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.userinfo.UserInfoDO;
import com.nj9394.module.safe.service.userinfo.UserInfoService;

@Tag(name = "管理后台 - 安全用户")
@RestController
@RequestMapping("/safe/user-info")
@Validated
public class UserInfoController {

    @Resource
    private UserInfoService userInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建安全用户")
    @PreAuthorize("@ss.hasPermission('safe:user-info:create')")
    public CommonResult<Long> createUserInfo(@Valid @RequestBody UserInfoSaveReqVO createReqVO) {
        return success(userInfoService.createUserInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新安全用户")
    @PreAuthorize("@ss.hasPermission('safe:user-info:update')")
    public CommonResult<Boolean> updateUserInfo(@Valid @RequestBody UserInfoSaveReqVO updateReqVO) {
        userInfoService.updateUserInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除安全用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:user-info:delete')")
    public CommonResult<Boolean> deleteUserInfo(@RequestParam("id") Long id) {
        userInfoService.deleteUserInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得安全用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:user-info:query')")
    public CommonResult<UserInfoRespVO> getUserInfo(@RequestParam("id") Long id) {
        UserInfoDO userInfo = userInfoService.getUserInfo(id);
        return success(BeanUtils.toBean(userInfo, UserInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得安全用户分页")
    @PreAuthorize("@ss.hasPermission('safe:user-info:query')")
    public CommonResult<PageResult<UserInfoRespVO>> getUserInfoPage(@Valid UserInfoPageReqVO pageReqVO) {
        PageResult<UserInfoDO> pageResult = userInfoService.getUserInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出安全用户 Excel")
    @PreAuthorize("@ss.hasPermission('safe:user-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserInfoExcel(@Valid UserInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserInfoDO> list = userInfoService.getUserInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "安全用户.xls", "数据", UserInfoRespVO.class,
                        BeanUtils.toBean(list, UserInfoRespVO.class));
    }

}