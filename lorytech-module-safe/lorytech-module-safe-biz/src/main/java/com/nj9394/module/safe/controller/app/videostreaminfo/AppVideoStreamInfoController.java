package com.nj9394.module.safe.controller.app.videostreaminfo;

import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.collection.CollectionUtils;
import com.nj9394.module.safe.controller.admin.videostreaminfo.vo.VideoStreamInfoWorkingRespVO;
import com.nj9394.module.safe.dal.dataobject.videostreaminfo.VideoStreamInfoDO;
import com.nj9394.module.safe.service.videostreaminfo.VideoStreamInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.Map;

import static com.nj9394.framework.common.pojo.CommonResult.success;

/**
 * 视频流信息 App API 控制器
 */
@Tag(name = "开放接口-视频流信息")
@RestController
@RequestMapping("/safe/video-stream-info")
@Validated
public class AppVideoStreamInfoController {

    @Resource
    private VideoStreamInfoService videoStreamInfoService;

    @GetMapping("/working-list")
    @Operation(summary = "获取工作中的视频流列表")
    @PermitAll
    public CommonResult<List<VideoStreamInfoWorkingRespVO>> getWorkingVideoStreamList() {
        List<Map<String, Object>> list = videoStreamInfoService.getWorkingVideoStreamWithAreaList();
        return success(CollectionUtils.convertList(list, item -> {
            VideoStreamInfoWorkingRespVO respVO = new VideoStreamInfoWorkingRespVO();
            // 复制基本属性
            respVO.setId(getLongValue(item, "id"));
            respVO.setCameraId(getLongValue(item, "camera_id"));
            respVO.setRtspAddress(getStringValue(item, "rtsp_address"));
            respVO.setVideoQuality(getStringValue(item, "video_quality"));
            respVO.setConcernedArea(getStringValue(item, "concerned_area"));
            respVO.setName(getStringValue(item, "name"));
            respVO.setIsWorking(getIntValue(item, "is_working"));
            // 设置区域ID
            respVO.setAreaId(getLongValue(item, "areaId"));
            return respVO;
        }));
    }
    
    // 辅助方法，从Map中获取Long类型的值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    // 辅助方法，从Map中获取String类型的值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    // 辅助方法，从Map中获取Integer类型的值
    private Integer getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
