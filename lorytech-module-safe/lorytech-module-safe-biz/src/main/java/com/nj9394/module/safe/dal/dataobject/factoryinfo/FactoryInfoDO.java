package com.nj9394.module.safe.dal.dataobject.factoryinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.nj9394.framework.mybatis.core.dataobject.BaseDO;

/**
 * 厂房信息 DO
 *
 * <AUTHOR>
 */
@TableName("safe_factory_info")
@KeySequence("safe_factory_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryInfoDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 厂房名称
     */
    private String name;
    /**
     * 平面图
     */
    private String floorPlan;
    /**
     * 面积
     */
    private String area;
    /**
     * 用途
     */
    private String purpose;

}