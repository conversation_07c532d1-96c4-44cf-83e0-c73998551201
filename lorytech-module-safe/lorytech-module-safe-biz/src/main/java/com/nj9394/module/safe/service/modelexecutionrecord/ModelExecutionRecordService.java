package com.nj9394.module.safe.service.modelexecutionrecord;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo.*;
import com.nj9394.module.safe.dal.dataobject.modelexecutionrecord.ModelExecutionRecordDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * 模型执行 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelExecutionRecordService {

    /**
     * 创建模型执行
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModelExecutionRecord(@Valid ModelExecutionRecordSaveReqVO createReqVO);

    /**
     * 更新模型执行
     *
     * @param updateReqVO 更新信息
     */
    void updateModelExecutionRecord(@Valid ModelExecutionRecordSaveReqVO updateReqVO);

    /**
     * 删除模型执行
     *
     * @param id 编号
     */
    void deleteModelExecutionRecord(Long id);

    /**
     * 获得模型执行
     *
     * @param id 编号
     * @return 模型执行
     */
    ModelExecutionRecordDO getModelExecutionRecord(Long id);

    /**
     * 获得模型执行分页
     *
     * @param pageReqVO 分页查询
     * @return 模型执行分页
     */
    PageResult<ModelExecutionRecordDO> getModelExecutionRecordPage(ModelExecutionRecordPageReqVO pageReqVO);

}