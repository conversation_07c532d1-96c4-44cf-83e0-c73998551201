package com.nj9394.module.safe.service.environmentdangerinfo;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.environmentdangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.environmentdangerinfo.EnvironmentDangerInfoDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * 危险环境分析 Service 接口
 *
 * <AUTHOR>
 */
public interface EnvironmentDangerInfoService {

    /**
     * 创建危险环境分析
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEnvironmentDangerInfo(@Valid EnvironmentDangerInfoSaveReqVO createReqVO);

    /**
     * 更新危险环境分析
     *
     * @param updateReqVO 更新信息
     */
    void updateEnvironmentDangerInfo(@Valid EnvironmentDangerInfoSaveReqVO updateReqVO);

    /**
     * 删除危险环境分析
     *
     * @param id 编号
     */
    void deleteEnvironmentDangerInfo(Long id);

    /**
     * 获得危险环境分析
     *
     * @param id 编号
     * @return 危险环境分析
     */
    EnvironmentDangerInfoDO getEnvironmentDangerInfo(Long id);

    /**
     * 获得危险环境分析分页
     *
     * @param pageReqVO 分页查询
     * @return 危险环境分析分页
     */
    PageResult<EnvironmentDangerInfoDO> getEnvironmentDangerInfoPage(EnvironmentDangerInfoPageReqVO pageReqVO);

}