package com.nj9394.module.safe.controller.admin.persondangerinfo.vo;

import com.nj9394.module.safe.utils.ImagePathConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 人员危险情况 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PersonDangerInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18376")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8225")
    @ExcelProperty("区域编号")
    private Long areaId;

    @Schema(description = "厂房")
    @ExcelProperty("厂房")
    private String factoryName;

    @Schema(description = "区域名称")
    @ExcelProperty("区域名称")
    private String areaName;

    @Schema(description = "是否佩戴安全帽")
    @ExcelProperty(value = "是否佩戴安全帽", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isWearingHelmet;

    @Schema(description = "是否穿着工作服")
    @ExcelProperty(value = "是否穿着工作服", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isWearingWorkClothes;

    @Schema(description = "是否出现危险行为")
    @ExcelProperty(value = "是否出现危险行为", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isDangerousBehavior;

    @Schema(description = "是否出现危险情况")
    @ExcelProperty(value = "是否出现危险情况", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isDangerousSituation;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDateTime recordTime;

    @Schema(description = "记录文件")
    @ExcelProperty(value = "记录文件", converter = ImagePathConverter.class)
    private String recordFile;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
