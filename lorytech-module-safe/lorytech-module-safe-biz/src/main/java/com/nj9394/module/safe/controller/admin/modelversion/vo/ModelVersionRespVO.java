package com.nj9394.module.safe.controller.admin.modelversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 模型版本 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelVersionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15234")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("模型名称")
    private String name;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private String version;

    @Schema(description = "上传时间")
    @ExcelProperty("上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("路径")
    private String addr;

}