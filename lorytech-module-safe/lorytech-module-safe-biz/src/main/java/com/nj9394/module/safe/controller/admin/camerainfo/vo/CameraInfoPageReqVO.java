package com.nj9394.module.safe.controller.admin.camerainfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.nj9394.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.nj9394.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 摄像头信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CameraInfoPageReqVO extends PageParam {

    @Schema(description = "厂房编号", example = "145")
    private Long factoryId;

    @Schema(description = "摄像头编号")
    private String number;

    @Schema(description = "摄像头类型", example = "1")
    private String type;

    @Schema(description = "工作状态（在线、离线等）", example = "2")
    private String status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}