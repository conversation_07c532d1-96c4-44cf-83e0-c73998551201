package com.nj9394.module.safe.controller.admin.emergencyplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 应急预案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EmergencyPlanRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11571")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "预案名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("预案名称")
    private String name;

    @Schema(description = "详细步骤")
    @ExcelProperty("详细步骤")
    private String steps;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}