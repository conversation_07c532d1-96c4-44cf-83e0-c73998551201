package com.nj9394.module.safe.controller.admin.factoryinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.factoryinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;
import com.nj9394.module.safe.service.factoryinfo.FactoryInfoService;
import com.nj9394.module.safe.dal.mysql.factoryinfo.FactoryInfoMapper;

@Tag(name = "管理后台 - 厂房信息")
@RestController
@RequestMapping("/safe/factory-info")
@Validated
public class FactoryInfoController {

    @Resource
    private FactoryInfoService factoryInfoService;
    
    @Resource
    private FactoryInfoMapper factoryInfoMapper;

    @PostMapping("/create")
    @Operation(summary = "创建厂房信息")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:create')")
    public CommonResult<Long> createFactoryInfo(@Valid @RequestBody FactoryInfoSaveReqVO createReqVO) {
        return success(factoryInfoService.createFactoryInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新厂房信息")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:update')")
    public CommonResult<Boolean> updateFactoryInfo(@Valid @RequestBody FactoryInfoSaveReqVO updateReqVO) {
        factoryInfoService.updateFactoryInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除厂房信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:factory-info:delete')")
    public CommonResult<Boolean> deleteFactoryInfo(@RequestParam("id") Long id) {
        factoryInfoService.deleteFactoryInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得厂房信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:query')")
    public CommonResult<FactoryInfoRespVO> getFactoryInfo(@RequestParam("id") Long id) {
        FactoryInfoDO factoryInfo = factoryInfoService.getFactoryInfo(id);
        return success(BeanUtils.toBean(factoryInfo, FactoryInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得厂房信息分页")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:query')")
    public CommonResult<PageResult<FactoryInfoRespVO>> getFactoryInfoPage(@Valid FactoryInfoPageReqVO pageReqVO) {
        PageResult<FactoryInfoDO> pageResult = factoryInfoService.getFactoryInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FactoryInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出厂房信息 Excel")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFactoryInfoExcel(@Valid FactoryInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FactoryInfoDO> list = factoryInfoService.getFactoryInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "厂房信息.xls", "数据", FactoryInfoRespVO.class,
                        BeanUtils.toBean(list, FactoryInfoRespVO.class));
    }

    @GetMapping("/factory-list")
    @Operation(summary = "获得厂房信息列表")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:query')")
    public CommonResult<List<FactoryInfoRespVO>> getFactoryInfoList(@Valid FactoryInfoPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FactoryInfoDO> list = factoryInfoService.getFactoryInfoPage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, FactoryInfoRespVO.class));
    }
    
    @GetMapping("/list")
    @Operation(summary = "获取所有厂房信息列表")
    @PreAuthorize("@ss.hasPermission('safe:factory-info:query')")
    public CommonResult<List<FactoryInfoRespVO>> getAllFactoryInfoList() {
        List<FactoryInfoDO> list = factoryInfoMapper.selectList();
        return success(BeanUtils.toBean(list, FactoryInfoRespVO.class));
    }
}