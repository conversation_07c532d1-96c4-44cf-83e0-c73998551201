package com.nj9394.module.safe.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 历史预警统计数据 Response VO")
@Data
public class AlarmInfoHistoryStatsRespVO {

    @Schema(description = "日期列表")
    private List<String> dates;

    @Schema(description = "预警数量列表")
    private List<Integer> counts;
    
    @Schema(description = "最大预警数")
    private Integer maxCount;
} 