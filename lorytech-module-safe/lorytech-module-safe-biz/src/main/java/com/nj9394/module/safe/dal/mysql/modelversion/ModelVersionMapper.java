package com.nj9394.module.safe.dal.mysql.modelversion;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.modelversion.ModelVersionDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.modelversion.vo.*;

/**
 * 模型版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelVersionMapper extends BaseMapperX<ModelVersionDO> {

    default PageResult<ModelVersionDO> selectPage(ModelVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelVersionDO>()
                .likeIfPresent(ModelVersionDO::getName, reqVO.getName())
                .eqIfPresent(ModelVersionDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(ModelVersionDO::getUploadTime, reqVO.getUploadTime())
                .betweenIfPresent(ModelVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelVersionDO::getId));
    }

}