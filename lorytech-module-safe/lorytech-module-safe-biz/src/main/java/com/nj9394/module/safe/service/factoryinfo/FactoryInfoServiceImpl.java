package com.nj9394.module.safe.service.factoryinfo;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoSaveReqVO;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;
import com.nj9394.module.safe.dal.mysql.factoryinfo.FactoryInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.FACTORY_INFO_NOT_EXISTS;

/**
 * 厂房信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FactoryInfoServiceImpl implements FactoryInfoService {

    @Resource
    private FactoryInfoMapper factoryInfoMapper;

    @Override
    public Long createFactoryInfo(FactoryInfoSaveReqVO createReqVO) {
        // 插入
        FactoryInfoDO factoryInfo = BeanUtils.toBean(createReqVO, FactoryInfoDO.class);
        factoryInfoMapper.insert(factoryInfo);
        // 返回
        return factoryInfo.getId();
    }

    @Override
    public void updateFactoryInfo(FactoryInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateFactoryInfoExists(updateReqVO.getId());
        // 更新
        FactoryInfoDO updateObj = BeanUtils.toBean(updateReqVO, FactoryInfoDO.class);
        factoryInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteFactoryInfo(Long id) {
        // 校验存在
        validateFactoryInfoExists(id);
        // 删除
        factoryInfoMapper.deleteById(id);
    }

    private void validateFactoryInfoExists(Long id) {
        if (factoryInfoMapper.selectById(id) == null) {
            throw exception(FACTORY_INFO_NOT_EXISTS);
        }
    }

    @Override
    public FactoryInfoDO getFactoryInfo(Long id) {
        return factoryInfoMapper.selectById(id);
    }

    @Override
    public PageResult<FactoryInfoDO> getFactoryInfoPage(FactoryInfoPageReqVO pageReqVO) {
        return factoryInfoMapper.selectPage(pageReqVO);
    }

}