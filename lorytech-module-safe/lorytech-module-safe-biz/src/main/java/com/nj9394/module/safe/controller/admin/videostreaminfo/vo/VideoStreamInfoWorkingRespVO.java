package com.nj9394.module.safe.controller.admin.videostreaminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 工作中的视频流信息 Response VO")
@Data
public class VideoStreamInfoWorkingRespVO {
    
    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;
    
    @Schema(description = "摄像头编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long cameraId;
    
    @Schema(description = "RTSP地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "rtsp://example.com/stream")
    private String rtspAddress;
    
    @Schema(description = "视频质量", requiredMode = Schema.RequiredMode.REQUIRED, example = "高清")
    private String videoQuality;
    
    @Schema(description = "关注区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "入口区域")
    private String concernedArea;
    
    @Schema(description = "别名", requiredMode = Schema.RequiredMode.REQUIRED, example = "入口摄像头")
    private String name;
    
    @Schema(description = "是否工作", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer isWorking;
    
    @Schema(description = "区域ID", example = "1")
    private Long areaId;
} 