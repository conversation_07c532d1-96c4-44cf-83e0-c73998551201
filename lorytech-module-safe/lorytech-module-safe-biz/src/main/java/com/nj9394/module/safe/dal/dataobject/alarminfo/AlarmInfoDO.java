package com.nj9394.module.safe.dal.dataobject.alarminfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.nj9394.framework.mybatis.core.dataobject.BaseDO;

/**
 * 报警信息 DO
 *
 * <AUTHOR>
 */
@TableName("safe_alarm_info")
@KeySequence("safe_alarm_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmInfoDO extends BaseDO {

    /**
     * 报警序号
     */
    @TableId
    private Long id;
    /**
     * 区域编号
     */
    private Long areaId;
    /**
     * 报警类型（如超员、未佩戴安全帽等）
     *
     * 枚举 {@link TODO safe_report_type 对应的类}
     */
    private String alarmType;
    /**
     * 报警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 确认状态（已确认、未确认）
     *
     * 枚举 {@link TODO infra_num_string 对应的类}
     */
    private String confirmedStatus;

    /**
     * 区域编号
     */
    private Long tenantId;

    /**
     * 告警图片
     */
    private String alarmPic;

}
