package com.nj9394.module.safe.controller.admin.camerainfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 摄像头信息新增/修改 Request VO")
@Data
public class CameraInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31735")
    private Long id;

    @Schema(description = "厂房编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "145")
    @NotNull(message = "厂房编号不能为空")
    private Long factoryId;

    @Schema(description = "摄像头编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "摄像头编号不能为空")
    private String number;

    @Schema(description = "摄像头类型", example = "1")
    private String type;

    @Schema(description = "安装位置")
    private String installationLocation;

    @Schema(description = "覆盖范围")
    private String coverageRange;

    @Schema(description = "工作状态（在线、离线等）", example = "2")
    private String status;

}