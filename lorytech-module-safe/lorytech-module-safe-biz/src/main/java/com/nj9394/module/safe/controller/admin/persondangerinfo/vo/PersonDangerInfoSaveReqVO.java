package com.nj9394.module.safe.controller.admin.persondangerinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 人员危险情况新增/修改 Request VO")
@Data
public class PersonDangerInfoSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "18376")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "8225")
    @NotNull(message = "区域编号不能为空")
    private Long areaId;

    @Schema(description = "是否佩戴安全帽")
    private Integer isWearingHelmet;

    @Schema(description = "是否穿着工作服")
    private Integer isWearingWorkClothes;

    @Schema(description = "是否出现危险行为")
    private Integer isDangerousBehavior;

    @Schema(description = "是否出现危险情况")
    private Integer isDangerousSituation;

    @Schema(description = "记录时间")
    private LocalDateTime recordTime;

    @Schema(description = "记录文件")
    private String recordFile;

}
