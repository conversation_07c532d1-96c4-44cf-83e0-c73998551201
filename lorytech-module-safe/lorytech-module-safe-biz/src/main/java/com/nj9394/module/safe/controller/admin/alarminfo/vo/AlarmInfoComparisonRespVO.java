package com.nj9394.module.safe.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 厂区报警环比数据 Response VO")
@Data
public class AlarmInfoComparisonRespVO {

    @Schema(description = "厂区名称列表")
    private List<String> factoryNames;

    @Schema(description = "本月报警数量列表")
    private List<Integer> currentMonthCounts;

    @Schema(description = "上月报警数量列表")
    private List<Integer> lastMonthCounts;
} 