package com.nj9394.module.safe.controller.admin.alarminfo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.nj9394.framework.common.util.collection.MapUtils;
import com.nj9394.module.safe.controller.admin.alarmsetting.vo.AlarmSettingRespVO;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.PersonDangerInfoRespVO;
import com.nj9394.module.safe.service.alarmsetting.AlarmSettingService;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertSet;

import com.nj9394.module.safe.controller.admin.alarminfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.alarminfo.AlarmInfoDO;
import com.nj9394.module.safe.service.alarminfo.AlarmInfoService;

@Tag(name = "管理后台 - 报警信息")
@RestController
@RequestMapping("/safe/alarm-info")
@Validated
public class AlarmInfoController {

    @Resource
    private AlarmInfoService alarmInfoService;

    @Resource
    private AlarmSettingService alarmSettingService;

    @Resource
    private AreaDivisionService areaDivisionService;

    @PostMapping("/create")
    @Operation(summary = "创建报警信息")
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:create')")
    public CommonResult<Long> createAlarmInfo(@Valid @RequestBody AlarmInfoSaveReqVO createReqVO) {
        return success(alarmInfoService.createAlarmInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新报警信息")
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:update')")
    public CommonResult<Boolean> updateAlarmInfo(@Valid @RequestBody AlarmInfoSaveReqVO updateReqVO) {
        alarmInfoService.updateAlarmInfo(updateReqVO);
        return success(true);
    }

    @PutMapping("/updateStatus")
    @Operation(summary = "更新报警信息")
    public CommonResult<Boolean> updateAlarmInfoStatus(@RequestParam("id") Long id, @RequestParam("status") String status) {
        alarmInfoService.updateAlarmInfoStatus(id, status);
        return success(true);
    }

    @PutMapping("/updateListStatus")
    @Operation(summary = "批量更新报警信息")
    public CommonResult<Integer> updateListStatus(@RequestParam("ids") String ids, @RequestParam("status") String status) {
        return success(alarmInfoService.updateAlarmInfoListStatus(ids, status));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除报警信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:delete')")
    public CommonResult<Boolean> deleteAlarmInfo(@RequestParam("id") Long id) {
        alarmInfoService.deleteAlarmInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得报警信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:query')")
    public CommonResult<AlarmInfoRespVO> getAlarmInfo(@RequestParam("id") Long id) {
        AlarmInfoDO alarmInfo = alarmInfoService.getAlarmInfo(id);
        return success(BeanUtils.toBean(alarmInfo, AlarmInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得报警信息分页")
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:query')")
    public CommonResult<PageResult<AlarmInfoRespVO>> getAlarmInfoPage(@Valid AlarmInfoPageReqVO pageReqVO) {
        PageResult<AlarmInfoDO> pageResult = alarmInfoService.getAlarmInfoPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, AlarmInfoRespVO.class));
        return success(buildStockVoPageResult(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出报警信息 Excel")
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAlarmInfoExcel(@Valid AlarmInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<AlarmInfoDO> list = alarmInfoService.getAlarmInfoPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "报警信息.xls", "数据", AlarmInfoRespVO.class,
//                        BeanUtils.toBean(list, AlarmInfoRespVO.class));
        List<AlarmInfoRespVO> list = buildStockVoPageResult(alarmInfoService.getAlarmInfoPage(pageReqVO)).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "定员分析.xls", "数据", AlarmInfoRespVO.class, list);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("报警信息", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        // 使用EasyExcel导出，支持图片嵌入
        EasyExcel.write(response.getOutputStream(), AlarmInfoRespVO.class)
                .autoCloseStream(false) // 不要自动关闭，交给Servlet自己处理
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                .sheet("数据")
                .doWrite(list);


    }

    private PageResult<AlarmInfoRespVO> buildStockVoPageResult(PageResult<AlarmInfoDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), AlarmInfoDO::getAreaId));
        Map<String, AlarmSettingRespVO> alarmSettingMap = alarmSettingService.getAlarmSettingMap(convertSet(pageResult.getList(), AlarmInfoDO::getAlarmType));
        return BeanUtils.toBean(pageResult, AlarmInfoRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
            MapUtils.findAndThen(alarmSettingMap, stock.getAlarmType(), setting -> stock.setPlanName(setting.getPlanName()).setPlanUrl(setting.getPlanUrl()));
        });
    }
    
    @GetMapping("/monthly-stats")
    @Operation(summary = "获取当月报警统计数据")
    public CommonResult<Map<Long, AlarmStatsRespVO>> getMonthlyAlarmStats() {
        return success(alarmInfoService.getAllFactoriesMonthlyAlarmStats());
    }
    
    @GetMapping("/monthly-stats-by-factory")
    @Operation(summary = "获取指定厂房的当月报警统计数据")
    @Parameter(name = "factoryId", description = "厂房ID", required = true)
    public CommonResult<AlarmStatsRespVO> getMonthlyAlarmStatsByFactory(@RequestParam("factoryId") Long factoryId) {
        return success(alarmInfoService.getMonthlyAlarmStats(factoryId));
    }

    @GetMapping("/history-stats")
    @Operation(summary = "获取历史预警统计数据")
    @Parameter(name = "factoryId", description = "厂房ID，不传则查询所有厂房", required = false)
    @Parameter(name = "days", description = "查询天数，默认30天", required = false)
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:query')")
    public CommonResult<AlarmInfoHistoryStatsRespVO> getHistoryAlarmStats(
            @RequestParam(value = "factoryId", required = false) Long factoryId,
            @RequestParam(value = "days", required = false, defaultValue = "30") Integer days) {
        return success(alarmInfoService.getHistoryAlarmStats(factoryId, days));
    }
    
    @GetMapping("/factory-comparison")
    @Operation(summary = "获取厂区报警环比数据")
    @Parameter(name = "factoryId", description = "厂房ID，不传则查询所有厂房", required = false)
    @PreAuthorize("@ss.hasPermission('safe:alarm-info:query')")
    public CommonResult<AlarmInfoComparisonRespVO> getFactoryAlarmComparison(
            @RequestParam(value = "factoryId", required = false) Long factoryId) {
        return success(alarmInfoService.getFactoryAlarmComparison(factoryId));
    }
}