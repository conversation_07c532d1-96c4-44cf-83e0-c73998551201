package com.nj9394.module.safe.service.alarmsetting;

import cn.hutool.core.collection.CollUtil;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.nj9394.module.safe.controller.admin.alarmsetting.vo.*;
import com.nj9394.module.safe.dal.dataobject.alarmsetting.AlarmSettingDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.alarmsetting.AlarmSettingMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertMap;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 报警配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmSettingServiceImpl implements AlarmSettingService {

    @Resource
    private AlarmSettingMapper alarmSettingMapper;

    @Override
    public Long createAlarmSetting(AlarmSettingSaveReqVO createReqVO) {
        Long count = alarmSettingMapper.selectCount("alarm_type", createReqVO.getAlarmType());
        if (count > 0) {
            throw exception(ALARM_SETTING_EXISTS);
        }
        // 插入
        AlarmSettingDO alarmSetting = BeanUtils.toBean(createReqVO, AlarmSettingDO.class);
        alarmSettingMapper.insert(alarmSetting);
        // 返回
        return alarmSetting.getId();
    }

    @Override
    public void updateAlarmSetting(AlarmSettingSaveReqVO updateReqVO) {
        // 校验存在
        validateAlarmSettingExists(updateReqVO.getId());
        AlarmSettingDO settingDO = alarmSettingMapper.selectById(updateReqVO.getId());
        if (!settingDO.getAlarmType().equals(updateReqVO.getAlarmType())) {
            Long count = alarmSettingMapper.selectCount("alarm_type", updateReqVO.getAlarmType());
            if (count > 0) {
                throw exception(ALARM_SETTING_EXISTS);
            }
        }
        // 更新
        AlarmSettingDO updateObj = BeanUtils.toBean(updateReqVO, AlarmSettingDO.class);
        alarmSettingMapper.updateById(updateObj);
    }

    @Override
    public void deleteAlarmSetting(Long id) {
        // 校验存在
        validateAlarmSettingExists(id);
        // 删除
        alarmSettingMapper.deleteById(id);
    }

    private void validateAlarmSettingExists(Long id) {
        if (alarmSettingMapper.selectById(id) == null) {
            throw exception(ALARM_SETTING_NOT_EXISTS);
        }
    }

    @Override
    public AlarmSettingDO getAlarmSetting(Long id) {
        return alarmSettingMapper.selectById(id);
    }

    @Override
    public PageResult<AlarmSettingDO> getAlarmSettingPage(AlarmSettingPageReqVO pageReqVO) {
        return alarmSettingMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<String, AlarmSettingRespVO> getAlarmSettingMap(Collection<String> types) {
        List<AlarmSettingRespVO> list;
        if (CollUtil.isEmpty(types)) {
            list = Collections.emptyList();
        }
        else {
            list = alarmSettingMapper.findListByTypes(types);
        }
        return convertMap(list, AlarmSettingRespVO::getAlarmType);
    }

}