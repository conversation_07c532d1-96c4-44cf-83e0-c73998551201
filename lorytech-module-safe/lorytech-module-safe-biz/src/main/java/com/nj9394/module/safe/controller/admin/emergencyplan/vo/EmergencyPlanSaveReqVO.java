package com.nj9394.module.safe.controller.admin.emergencyplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 应急预案新增/修改 Request VO")
@Data
public class EmergencyPlanSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11571")
    private Long id;

    @Schema(description = "预案名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "预案名称不能为空")
    private String name;

    @Schema(description = "详细步骤")
    private String steps;

}