package com.nj9394.module.safe.controller.admin.historicaldataquery;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.nj9394.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nj9394.framework.common.util.collection.MapUtils;
import com.nj9394.framework.common.util.io.IoUtils;
import com.nj9394.framework.excel.core.handler.SelectSheetWriteHandler;
import com.nj9394.module.infra.api.file.FileApi;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.PersonDangerInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.PersonDangerInfoRespVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisBaseVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisPageReqVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisRespVO;
import com.nj9394.module.safe.dal.dataobject.persondangerinfo.PersonDangerInfoDO;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.enums.EnumDataType;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import com.nj9394.module.safe.service.persondangerinfo.PersonDangerInfoService;
import com.nj9394.module.safe.service.quotaanalysis.QuotaAnalysisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;

import static com.nj9394.framework.common.pojo.CommonResult.error;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertSet;

import com.nj9394.module.safe.controller.admin.historicaldataquery.vo.*;
import com.nj9394.module.safe.dal.dataobject.historicaldataquery.HistoricalDataQueryDO;
import com.nj9394.module.safe.service.historicaldataquery.HistoricalDataQueryService;

@Tag(name = "管理后台 - 安全分析数据归档")
@RestController
@RequestMapping("/safe/historical-data-query")
@Validated
public class HistoricalDataQueryController {

    @Resource
    private HistoricalDataQueryService historicalDataQueryService;

    @Resource
    private QuotaAnalysisService quotaAnalysisService;

    @Resource
    private PersonDangerInfoService personDangerInfoService;

    @Resource
    private AreaDivisionService areaDivisionService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private FileApi fileApi;

    @Value("${lorytech.temp-path}")
    private String TEMP_PATH;


    @PostMapping("/create")
    @Operation(summary = "创建安全分析数据归档")
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:create')")
    public CommonResult<Long> createHistoricalDataQuery(@Valid @RequestBody HistoricalDataQuerySaveReqVO createReqVO) throws FileNotFoundException {
        // 获取数据
        // 开始时间
        createReqVO.getDataStime().setHours(0);
        createReqVO.getDataStime().setMinutes(0);
        createReqVO.getDataStime().setSeconds(0);
        // 结束时间
        createReqVO.getDataEtime().setHours(23);
        createReqVO.getDataEtime().setMinutes(59);
        createReqVO.getDataEtime().setSeconds(59);

        // 将Date转换为LocalDate
        LocalDateTime localDate1 = createReqVO.getDataStime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime localDate2 = createReqVO.getDataEtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 计算两个日期之间的天数差
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(localDate1, localDate2);
        if (daysBetween > 30) {
            return error(GlobalErrorCodeConstants.ERROR_CONFIGURATION.getCode(), "时间范围不能超过30天");
        }

        // 文件地址
        String fileName = MD5.create().digestHex(createReqVO.getDataTitle()) + ".xls";
        String filePath = TEMP_PATH + File.separator + fileName;
        if (createReqVO.getDataType().equals(EnumDataType.COUNT.getType())) {
            // 查询参数
            QuotaAnalysisPageReqVO pageReqVO = new QuotaAnalysisPageReqVO();
            pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            pageReqVO.setAreaId(createReqVO.getAreaId());
            LocalDateTime[] times = new LocalDateTime[2];
            times[0] = localDate1;
            times[1] = localDate2;
            pageReqVO.setRecordTime(times);

            // 定员分析
            List<QuotaAnalysisRespVO> list = buildQuotaAnalysisRespVOPageResult(quotaAnalysisService.getQuotaAnalysisPage(pageReqVO)).getList();

            // 数据量
            if (list != null && list.size() > 0) {
                // 输出Excel
                EasyExcel.write(filePath, QuotaAnalysisRespVO.class).sheet("定员分析数据").doWrite(list);
                File file = new File(filePath);
                if (file.exists()) {
                    // 保存地址
                    createReqVO.setDataFile(fileApi.createFile(fileName, FileUtil.readBytes(file)));
                    file.delete();
                }
            }
            else {
                return error(GlobalErrorCodeConstants.ERROR_CONFIGURATION.getCode(), "该时间范围内暂无定员分析数据");
            }

        }
        else if (createReqVO.getDataType().equals(EnumDataType.DANGER.getType())) {
            // 危险情况分析
            PersonDangerInfoPageReqVO pageReqVO = new PersonDangerInfoPageReqVO();
            pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            pageReqVO.setAreaId(createReqVO.getAreaId());
            LocalDateTime[] times = new LocalDateTime[2];
            times[0] = localDate1;
            times[1] = localDate2;
            pageReqVO.setRecordTime(times);

            // 危险分析数据
            List<PersonDangerInfoRespVO> list = buildPersonDangerInfoPageReqVOPageResult(personDangerInfoService.getPersonDangerInfoPage(pageReqVO)).getList();
            // 数据量
            if (list != null && list.size() > 0) {
                // 生成excel文件
                EasyExcel.write(filePath, PersonDangerInfoRespVO.class).sheet("危险情况分析").doWrite(list);
                File file = new File(filePath);
                if (file.exists()) {
                    // 保存地址
                    createReqVO.setDataFile(fileApi.createFile(fileName, FileUtil.readBytes(file)));
                    file.delete();
                }
            }
            else {
                return error(GlobalErrorCodeConstants.ERROR_CONFIGURATION.getCode(), "该时间范围内暂无危险情况分析数据");
            }

        }
        if (createReqVO.getDataFile() == null || createReqVO.getDataFile().length() < 1) {
            return error(GlobalErrorCodeConstants.ERROR_CONFIGURATION.getCode(), "分析数据归档文件生成失败");
        }
        return success(historicalDataQueryService.createHistoricalDataQuery(createReqVO));
    }

    /**
     * 数据转换
     */
    private PageResult<QuotaAnalysisRespVO> buildQuotaAnalysisRespVOPageResult(PageResult<QuotaAnalysisDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), QuotaAnalysisDO::getAreaId));
        return BeanUtils.toBean(pageResult, QuotaAnalysisRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
        });
    }

    private PageResult<PersonDangerInfoRespVO> buildPersonDangerInfoPageReqVOPageResult(PageResult<PersonDangerInfoDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), PersonDangerInfoDO::getAreaId));
        return BeanUtils.toBean(pageResult, PersonDangerInfoRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
        });
    }

    @PutMapping("/update")
    @Operation(summary = "更新安全分析数据归档")
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:update')")
    public CommonResult<Boolean> updateHistoricalDataQuery(@Valid @RequestBody HistoricalDataQuerySaveReqVO updateReqVO) {
        historicalDataQueryService.updateHistoricalDataQuery(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除安全分析数据归档")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:delete')")
    public CommonResult<Boolean> deleteHistoricalDataQuery(@RequestParam("id") Long id) {
        historicalDataQueryService.deleteHistoricalDataQuery(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得安全分析数据归档")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:query')")
    public CommonResult<HistoricalDataQueryRespVO> getHistoricalDataQuery(@RequestParam("id") Long id) {
        HistoricalDataQueryDO historicalDataQuery = historicalDataQueryService.getHistoricalDataQuery(id);
        return success(BeanUtils.toBean(historicalDataQuery, HistoricalDataQueryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得安全分析数据归档分页")
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:query')")
    public CommonResult<PageResult<HistoricalDataQueryRespVO>> getHistoricalDataQueryPage(@Valid HistoricalDataQueryPageReqVO pageReqVO) {
        PageResult<HistoricalDataQueryDO> pageResult = historicalDataQueryService.getHistoricalDataQueryPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, HistoricalDataQueryRespVO.class));
        return success(buildStockVoPageResult(pageResult));

    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出安全分析数据归档 Excel")
    @PreAuthorize("@ss.hasPermission('safe:historical-data-query:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHistoricalDataQueryExcel(@Valid HistoricalDataQueryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<HistoricalDataQueryDO> list = historicalDataQueryService.getHistoricalDataQueryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "安全分析数据归档.xls", "数据", HistoricalDataQueryRespVO.class,
                        BeanUtils.toBean(list, HistoricalDataQueryRespVO.class));
    }

    private PageResult<HistoricalDataQueryRespVO> buildStockVoPageResult(PageResult<HistoricalDataQueryDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, AreaDivisionRespVO> areaDivisionMap = areaDivisionService.getAreaDivisionMap(convertSet(pageResult.getList(), HistoricalDataQueryDO::getAreaId));
        return BeanUtils.toBean(pageResult, HistoricalDataQueryRespVO.class, stock -> {
            MapUtils.findAndThen(areaDivisionMap, stock.getAreaId(), area -> stock.setAreaName(area.getName()).setFactoryName(area.getFactoryName()));
        });
    }

}