package com.nj9394.module.safe.controller.admin.environmentdangerinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.nj9394.framework.excel.core.annotations.DictFormat;
import com.nj9394.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 危险环境分析 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EnvironmentDangerInfoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20046")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "26091")
    @ExcelProperty("区域编号")
    private Long areaId;

    @Schema(description = "是否有火情")
    @ExcelProperty(value = "是否有火情", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isFire;

    @Schema(description = "是否有爆燃")
    @ExcelProperty(value = "是否有爆燃", converter = DictConvert.class)
    @DictFormat("infra_num_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isExplosion;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDateTime recordTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
