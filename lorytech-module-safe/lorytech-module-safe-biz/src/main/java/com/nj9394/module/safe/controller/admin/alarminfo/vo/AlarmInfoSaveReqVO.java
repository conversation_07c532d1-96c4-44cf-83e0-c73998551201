package com.nj9394.module.safe.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 报警信息新增/修改 Request VO")
@Data
public class AlarmInfoSaveReqVO {

    @Schema(description = "报警序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "31220")
    private Long id;

    @Schema(description = "区域编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17900")
    @NotNull(message = "区域编号不能为空")
    private Long areaId;

    @Schema(description = "报警类型（如超员、未佩戴安全帽等）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "报警类型（如超员、未佩戴安全帽等）不能为空")
    private String alarmType;

    @Schema(description = "报警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "确认状态（已确认、未确认）", example = "1")
    private String confirmedStatus;

    @Schema(description = "报警图片")
    private String alarmPic;

}