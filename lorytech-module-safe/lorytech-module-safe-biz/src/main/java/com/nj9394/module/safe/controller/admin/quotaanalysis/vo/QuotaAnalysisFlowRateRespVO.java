package com.nj9394.module.safe.controller.admin.quotaanalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 厂区人流量仪表盘数据 Response VO")
@Data
public class QuotaAnalysisFlowRateRespVO {

    @Schema(description = "当前人数")
    private Integer currentCount;

    @Schema(description = "最大允许人数")
    private Integer maxAllowance;

    @Schema(description = "最大限制人数")
    private Integer maxLimit;

    @Schema(description = "最大严重人数")
    private Integer maxSerious;
} 