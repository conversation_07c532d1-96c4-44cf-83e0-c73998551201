package com.nj9394.module.safe.dal.dataobject.persondangerinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.nj9394.framework.mybatis.core.dataobject.BaseDO;

/**
 * 人员危险情况历史 DO
 *
 * <AUTHOR>
 */
@TableName("safe_person_danger_info_history")
@KeySequence("safe_person_danger_info_history_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonDangerInfoHistoryDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 区域编号
     */
    private Long areaId;
    /**
     * 是否佩戴安全帽
     *
     * 枚举 {@link TODO infra_num_string 对应的类}
     */
    private Integer isWearingHelmet;
    /**
     * 是否穿着工作服
     *
     * 枚举 {@link TODO infra_num_string 对应的类}
     */
    private Integer isWearingWorkClothes;
    /**
     * 是否出现危险行为
     *
     * 枚举 {@link TODO infra_num_string 对应的类}
     */
    private Integer isDangerousBehavior;
    /**
     * 是否出现危险情况
     *
     * 枚举 {@link TODO infra_num_string 对应的类}
     */
    private Integer isDangerousSituation;
    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
    /**
     * 记录文件
     */
    private String recordFile;

} 