package com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 模型执行新增/修改 Request VO")
@Data
public class ModelExecutionRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "22496")
    private Long id;

    @Schema(description = "模型编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14145")
    @NotNull(message = "模型编号不能为空")
    private Long modelId;

    @Schema(description = "执行时间")
    private LocalDateTime executionTime;

    @Schema(description = "处理速度")
    private String processingSpeed;

    @Schema(description = "识别准确率")
    private String recognitionAccuracy;

    @Schema(description = "执行时长")
    private String executionDuration;

}