package com.nj9394.module.safe.controller.admin.datastorage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 数据存储 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DataStorageRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "29314")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "监控录像及分析数据（基于文件路径或二进制存储）")
    @ExcelProperty("监控录像及分析数据（基于文件路径或二进制存储）")
    private byte[] monitoringData;

    @Schema(description = "备份时间")
    @ExcelProperty("备份时间")
    private LocalDateTime backupTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}