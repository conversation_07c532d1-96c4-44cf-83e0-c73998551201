package com.nj9394.module.safe.controller.admin.emergencyplan;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.emergencyplan.vo.*;
import com.nj9394.module.safe.dal.dataobject.emergencyplan.EmergencyPlanDO;
import com.nj9394.module.safe.service.emergencyplan.EmergencyPlanService;

@Tag(name = "管理后台 - 应急预案")
@RestController
@RequestMapping("/safe/emergency-plan")
@Validated
public class EmergencyPlanController {

    @Resource
    private EmergencyPlanService emergencyPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建应急预案")
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:create')")
    public CommonResult<Long> createEmergencyPlan(@Valid @RequestBody EmergencyPlanSaveReqVO createReqVO) {
        return success(emergencyPlanService.createEmergencyPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新应急预案")
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:update')")
    public CommonResult<Boolean> updateEmergencyPlan(@Valid @RequestBody EmergencyPlanSaveReqVO updateReqVO) {
        emergencyPlanService.updateEmergencyPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除应急预案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:delete')")
    public CommonResult<Boolean> deleteEmergencyPlan(@RequestParam("id") Long id) {
        emergencyPlanService.deleteEmergencyPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得应急预案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:query')")
    public CommonResult<EmergencyPlanRespVO> getEmergencyPlan(@RequestParam("id") Long id) {
        EmergencyPlanDO emergencyPlan = emergencyPlanService.getEmergencyPlan(id);
        return success(BeanUtils.toBean(emergencyPlan, EmergencyPlanRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得应急预案分页")
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:query')")
    public CommonResult<PageResult<EmergencyPlanRespVO>> getEmergencyPlanPage(@Valid EmergencyPlanPageReqVO pageReqVO) {
        PageResult<EmergencyPlanDO> pageResult = emergencyPlanService.getEmergencyPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmergencyPlanRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出应急预案 Excel")
    @PreAuthorize("@ss.hasPermission('safe:emergency-plan:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmergencyPlanExcel(@Valid EmergencyPlanPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmergencyPlanDO> list = emergencyPlanService.getEmergencyPlanPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "应急预案.xls", "数据", EmergencyPlanRespVO.class,
                        BeanUtils.toBean(list, EmergencyPlanRespVO.class));
    }

}