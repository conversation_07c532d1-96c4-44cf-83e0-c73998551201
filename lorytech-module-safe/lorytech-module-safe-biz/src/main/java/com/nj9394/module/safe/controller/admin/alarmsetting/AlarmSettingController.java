package com.nj9394.module.safe.controller.admin.alarmsetting;

import cn.hutool.core.collection.CollUtil;
import com.nj9394.framework.common.util.collection.MapUtils;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.emergencyplan.vo.EmergencyPlanRespVO;
import com.nj9394.module.safe.controller.admin.quotaanalysis.vo.QuotaAnalysisRespVO;
import com.nj9394.module.safe.dal.dataobject.quotaanalysis.QuotaAnalysisDO;
import com.nj9394.module.safe.service.emergencyplan.EmergencyPlanService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;
import static com.nj9394.framework.common.util.collection.CollectionUtils.convertSet;

import com.nj9394.module.safe.controller.admin.alarmsetting.vo.*;
import com.nj9394.module.safe.dal.dataobject.alarmsetting.AlarmSettingDO;
import com.nj9394.module.safe.service.alarmsetting.AlarmSettingService;

@Tag(name = "管理后台 - 报警配置")
@RestController
@RequestMapping("/safe/alarm-setting")
@Validated
public class AlarmSettingController {

    @Resource
    private AlarmSettingService alarmSettingService;

    @Resource
    private EmergencyPlanService emergencyPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建报警配置")
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:create')")
    public CommonResult<Long> createAlarmSetting(@Valid @RequestBody AlarmSettingSaveReqVO createReqVO) {
        return success(alarmSettingService.createAlarmSetting(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新报警配置")
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:update')")
    public CommonResult<Boolean> updateAlarmSetting(@Valid @RequestBody AlarmSettingSaveReqVO updateReqVO) {
        alarmSettingService.updateAlarmSetting(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除报警配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:delete')")
    public CommonResult<Boolean> deleteAlarmSetting(@RequestParam("id") Long id) {
        alarmSettingService.deleteAlarmSetting(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得报警配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:query')")
    public CommonResult<AlarmSettingRespVO> getAlarmSetting(@RequestParam("id") Long id) {
        AlarmSettingDO alarmSetting = alarmSettingService.getAlarmSetting(id);
        return success(BeanUtils.toBean(alarmSetting, AlarmSettingRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得报警配置分页")
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:query')")
    public CommonResult<PageResult<AlarmSettingRespVO>> getAlarmSettingPage(@Valid AlarmSettingPageReqVO pageReqVO) {
        PageResult<AlarmSettingDO> pageResult = alarmSettingService.getAlarmSettingPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, AlarmSettingRespVO.class));
        return success(buildStockVoPageResult(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出报警配置 Excel")
    @PreAuthorize("@ss.hasPermission('safe:alarm-setting:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAlarmSettingExcel(@Valid AlarmSettingPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<AlarmSettingDO> list = alarmSettingService.getAlarmSettingPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "报警配置.xls", "数据", AlarmSettingRespVO.class,
//                        BeanUtils.toBean(list, AlarmSettingRespVO.class));

        List<AlarmSettingRespVO> list = buildStockVoPageResult(alarmSettingService.getAlarmSettingPage(pageReqVO)).getList();
        // 导出 Excel
        ExcelUtils.write(response, "报警配置.xls", "数据", AlarmSettingRespVO.class, list);

    }

    private PageResult<AlarmSettingRespVO> buildStockVoPageResult(PageResult<AlarmSettingDO> pageResult) {
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }
        Map<Long, EmergencyPlanRespVO> planMap = emergencyPlanService.getPlanMap(convertSet(pageResult.getList(), AlarmSettingDO::getPlanId));
        return BeanUtils.toBean(pageResult, AlarmSettingRespVO.class, stock -> {
            MapUtils.findAndThen(planMap, stock.getPlanId(), plan -> stock.setPlanName(plan.getName()).setPlanUrl(plan.getSteps()));
        });
    }

}