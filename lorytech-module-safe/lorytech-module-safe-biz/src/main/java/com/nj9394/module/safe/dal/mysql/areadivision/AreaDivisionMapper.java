package com.nj9394.module.safe.dal.mysql.areadivision;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.areadivision.AreaDivisionDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.areadivision.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 区域划分 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AreaDivisionMapper extends BaseMapperX<AreaDivisionDO> {

    default PageResult<AreaDivisionDO> selectPage(AreaDivisionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AreaDivisionDO>()
                .eqIfPresent(AreaDivisionDO::getFactoryId, reqVO.getFactoryId())
                .likeIfPresent(AreaDivisionDO::getName, reqVO.getName())
                .eqIfPresent(AreaDivisionDO::getDangerLevel, reqVO.getDangerLevel())
                .betweenIfPresent(AreaDivisionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AreaDivisionDO::getId));
    }

    List<AreaDivisionRespVO> findListByIds(@Param("ids") Collection<Long> ids);

    List<AreaDivisionRespVO> findAllList();

}