package com.nj9394.module.safe.service.python;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nj9394.module.safe.controller.admin.alarminfo.vo.AlarmInfoSaveReqVO;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.PersonDangerInfoSaveReqVO;
import com.nj9394.module.safe.service.alarminfo.AlarmInfoService;
import com.nj9394.module.safe.service.persondangerinfo.PersonDangerInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Base64;

/**
 * Python批处理服务类，用于批量处理多个视频流的图片
 */
@Service
public class PythonBatchService {

    private static final Logger logger = LoggerFactory.getLogger(PythonBatchService.class);
    private static final String PYTHON_MODEL_DIR = System.getProperty("user.dir") + "/model_het";
    private static final String PYTHON_SCRIPT = "batchImageToResult.py";
    private static final String REDIS_KEY_PREFIX = "het_img:";
    private static final int BATCH_SIZE = 8; // 批处理大小
    private static final int BATCH_TIMEOUT_MS = 2000; // 批处理超时时间（毫秒）
    private static final int FRAME_CAPTURE_INTERVAL = 1; // 帧采集间隔（秒）

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PersonDangerInfoService personDangerInfoService;
    
    @Resource
    private AlarmInfoService alarmInfoService;

    @Value("${safe.danger-url}")
    private String dangerUrl;

    // Python批处理进程
    private Process batchProcess;
    private BufferedWriter processInput;
    private BufferedReader processOutput;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 视频流管理
    private final Map<String, VideoStreamCapture> videoStreams = new ConcurrentHashMap<>();
    private final ExecutorService captureExecutor = Executors.newCachedThreadPool();
    
    // 批处理队列
    private final BlockingQueue<ImageProcessRequest> imageQueue = new LinkedBlockingQueue<>();
    private final ExecutorService batchProcessor = Executors.newSingleThreadExecutor();

    /**
     * 图片处理请求
     */
    public static class ImageProcessRequest {
        private final String streamKey;
        private final Map<String, Object> streamInfo;
        private final byte[] imageData;
        private final long timestamp;

        public ImageProcessRequest(String streamKey, Map<String, Object> streamInfo, byte[] imageData) {
            this.streamKey = streamKey;
            this.streamInfo = streamInfo;
            this.imageData = imageData;
            this.timestamp = System.currentTimeMillis();
        }

        // Getters
        public String getStreamKey() { return streamKey; }
        public Map<String, Object> getStreamInfo() { return streamInfo; }
        public byte[] getImageData() { return imageData; }
        public long getTimestamp() { return timestamp; }
    }

    /**
     * 视频流采集器
     */
    private static class VideoStreamCapture {
        private final String streamKey;
        private final Map<String, Object> streamInfo;
        private final String rtspUrl;
        private final AtomicBoolean running = new AtomicBoolean(true);
        private Future<?> captureTask;

        public VideoStreamCapture(String streamKey, Map<String, Object> streamInfo, String rtspUrl) {
            this.streamKey = streamKey;
            this.streamInfo = streamInfo;
            this.rtspUrl = rtspUrl;
        }

        public void start(PythonBatchService service) {
            captureTask = service.captureExecutor.submit(() -> service.captureFrames(this));
        }

        public void stop() {
            running.set(false);
            if (captureTask != null) {
                captureTask.cancel(true);
            }
        }

        // Getters
        public String getStreamKey() { return streamKey; }
        public Map<String, Object> getStreamInfo() { return streamInfo; }
        public String getRtspUrl() { return rtspUrl; }
        public AtomicBoolean getRunning() { return running; }
    }

    /**
     * 启动批处理服务
     */
    public void startBatchService() {
        if (isRunning.get()) {
            logger.warn("批处理服务已经在运行中");
            return;
        }

        try {
            // 启动Python批处理进程
            startBatchProcess();
            
            // 启动批处理任务
            startBatchProcessor();
            
            isRunning.set(true);
            logger.info("批处理服务启动成功");
        } catch (Exception e) {
            logger.error("启动批处理服务失败", e);
            throw new RuntimeException("启动批处理服务失败", e);
        }
    }

    /**
     * 停止批处理服务
     */
    public void stopBatchService() {
        if (!isRunning.get()) {
            return;
        }

        logger.info("开始停止批处理服务...");
        isRunning.set(false);

        // 停止所有视频流采集
        for (VideoStreamCapture capture : videoStreams.values()) {
            capture.stop();
        }
        videoStreams.clear();

        // 关闭线程池
        captureExecutor.shutdown();
        batchProcessor.shutdown();

        try {
            if (!captureExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                captureExecutor.shutdownNow();
            }
            if (!batchProcessor.awaitTermination(5, TimeUnit.SECONDS)) {
                batchProcessor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            captureExecutor.shutdownNow();
            batchProcessor.shutdownNow();
        }

        // 关闭Python进程
        if (batchProcess != null) {
            try {
                if (processInput != null) {
                    processInput.close();
                }
                if (processOutput != null) {
                    processOutput.close();
                }
                batchProcess.destroy();
                if (!batchProcess.waitFor(5, TimeUnit.SECONDS)) {
                    batchProcess.destroyForcibly();
                }
            } catch (Exception e) {
                logger.error("关闭Python进程时出错", e);
            }
        }

        logger.info("批处理服务已停止");
    }

    /**
     * 添加视频流
     */
    public void addVideoStream(Map<String, Object> streamInfo, String rtspUrl) {
        Long id = getLongValue(streamInfo, "id");
        Long cameraId = getLongValue(streamInfo, "camera_id");
        
        if (id == null || cameraId == null) {
            logger.error("视频流信息不完整，无法添加");
            return;
        }

        String streamKey = id + ":" + cameraId;
        
        if (videoStreams.containsKey(streamKey)) {
            logger.warn("视频流已存在: {}", streamKey);
            return;
        }

        VideoStreamCapture capture = new VideoStreamCapture(streamKey, streamInfo, rtspUrl);
        videoStreams.put(streamKey, capture);
        capture.start(this);
        
        logger.info("已添加视频流: {}", streamKey);
    }

    /**
     * 移除视频流
     */
    public void removeVideoStream(Long id, Long cameraId) {
        String streamKey = id + ":" + cameraId;
        VideoStreamCapture capture = videoStreams.remove(streamKey);
        if (capture != null) {
            capture.stop();
            logger.info("已移除视频流: {}", streamKey);
        }
    }

    /**
     * 启动Python批处理进程
     */
    private void startBatchProcess() throws IOException {
        File scriptFile = new File(PYTHON_MODEL_DIR, PYTHON_SCRIPT);
        String scriptPath = scriptFile.getAbsolutePath();

        ProcessBuilder processBuilder = new ProcessBuilder(
                "python", scriptPath,
                "--batch-size", String.valueOf(BATCH_SIZE),
                "--timeout", String.valueOf(BATCH_TIMEOUT_MS)
        );

        processBuilder.directory(new File(PYTHON_MODEL_DIR));
        processBuilder.redirectErrorStream(false);

        batchProcess = processBuilder.start();
        processInput = new BufferedWriter(new OutputStreamWriter(batchProcess.getOutputStream()));
        processOutput = new BufferedReader(new InputStreamReader(batchProcess.getInputStream()));

        logger.info("Python批处理进程已启动");
    }

    /**
     * 启动批处理任务
     */
    private void startBatchProcessor() {
        batchProcessor.submit(() -> {
            List<ImageProcessRequest> batch = new ArrayList<>();
            
            while (isRunning.get()) {
                try {
                    // 收集批处理数据
                    ImageProcessRequest request = imageQueue.poll(BATCH_TIMEOUT_MS, TimeUnit.MILLISECONDS);
                    if (request != null) {
                        batch.add(request);
                    }

                    // 继续收集直到达到批大小或超时
                    while (batch.size() < BATCH_SIZE && isRunning.get()) {
                        request = imageQueue.poll(100, TimeUnit.MILLISECONDS);
                        if (request == null) {
                            break;
                        }
                        batch.add(request);
                    }

                    // 处理批次
                    if (!batch.isEmpty()) {
                        processBatch(batch);
                        batch.clear();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("批处理任务出错", e);
                }
            }
        });
    }

    /**
     * 采集视频帧
     */
    private void captureFrames(VideoStreamCapture capture) {
        String streamKey = capture.getStreamKey();
        logger.info("开始采集视频流: {}", streamKey);

        try {
            // 使用OpenCV采集视频帧（这里简化为模拟实现）
            // 实际实现中需要使用OpenCV Java绑定或JNI调用
            while (capture.getRunning().get() && isRunning.get()) {
                try {
                    // 模拟采集帧（实际实现需要从RTSP流读取）
                    byte[] frameData = captureFrameFromRTSP(capture.getRtspUrl());

                    if (frameData != null) {
                        ImageProcessRequest request = new ImageProcessRequest(
                            streamKey, capture.getStreamInfo(), frameData);

                        // 添加到处理队列，如果队列满了则丢弃旧的帧
                        if (!imageQueue.offer(request)) {
                            // 队列满了，移除最旧的请求
                            imageQueue.poll();
                            imageQueue.offer(request);
                        }
                    }

                    Thread.sleep(FRAME_CAPTURE_INTERVAL * 1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("采集视频流帧时出错: {}", streamKey, e);
                    Thread.sleep(5000); // 出错后等待5秒再重试
                }
            }
        } catch (Exception e) {
            logger.error("视频流采集线程异常退出: {}", streamKey, e);
        }

        logger.info("视频流采集结束: {}", streamKey);
    }

    /**
     * 从RTSP流采集帧
     * 使用FFmpeg命令行工具采集单帧
     */
    private byte[] captureFrameFromRTSP(String rtspUrl) {
        try {
            // 使用FFmpeg采集单帧
            ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg",
                "-i", rtspUrl,
                "-vframes", "1",
                "-f", "image2",
                "-c:v", "mjpeg",
                "-"
            );

            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出流
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (InputStream is = process.getInputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
            }

            // 等待进程完成
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return null;
            }

            if (process.exitValue() == 0) {
                byte[] frameData = baos.toByteArray();
                return frameData.length > 0 ? frameData : null;
            }

        } catch (Exception e) {
            logger.debug("采集RTSP帧失败: {}", rtspUrl, e);
        }

        return null;
    }

    /**
     * 处理批次
     */
    private void processBatch(List<ImageProcessRequest> batch) {
        if (batch.isEmpty()) {
            return;
        }

        try {
            logger.debug("开始处理批次，大小: {}", batch.size());

            // 构建批处理请求
            Map<String, Object> batchRequest = new HashMap<>();
            batchRequest.put("batch_size", batch.size());

            List<Map<String, Object>> images = new ArrayList<>();
            for (int i = 0; i < batch.size(); i++) {
                ImageProcessRequest request = batch.get(i);
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("stream_key", request.getStreamKey());
                imageInfo.put("image_data", Base64.getEncoder().encodeToString(request.getImageData()));
                imageInfo.put("timestamp", request.getTimestamp());
                images.add(imageInfo);
            }
            batchRequest.put("images", images);

            // 发送到Python进程
            String requestJson = objectMapper.writeValueAsString(batchRequest);
            processInput.write(requestJson);
            processInput.newLine();
            processInput.flush();

            // 读取响应
            String responseJson = processOutput.readLine();
            if (responseJson != null) {
                Map<String, Object> response = objectMapper.readValue(responseJson, Map.class);
                handleBatchResponse(batch, response);
            }

        } catch (Exception e) {
            logger.error("处理批次时出错", e);
        }
    }

    /**
     * 处理批次响应
     */
    @SuppressWarnings("unchecked")
    private void handleBatchResponse(List<ImageProcessRequest> batch, Map<String, Object> response) {
        try {
            List<Map<String, Object>> results = (List<Map<String, Object>>) response.get("results");
            if (results == null || results.size() != batch.size()) {
                logger.error("批次响应结果数量不匹配，期望: {}, 实际: {}",
                    batch.size(), results != null ? results.size() : 0);
                return;
            }

            for (int i = 0; i < batch.size(); i++) {
                ImageProcessRequest request = batch.get(i);
                Map<String, Object> result = results.get(i);

                // 保存结果到Redis
                String redisKey = REDIS_KEY_PREFIX + request.getStreamKey();
                saveToRedis(redisKey, request.getStreamInfo(), result);
            }

        } catch (Exception e) {
            logger.error("处理批次响应时出错", e);
        }
    }

    /**
     * 保存到Redis（复用原有逻辑）
     */
    private void saveToRedis(String redisKey, Map<String, Object> streamInfo, Map<String, Object> result) {
        try {
            // 保存视频流信息（仅在首次保存）
            if (!redisTemplate.hasKey(redisKey)) {
                for (Map.Entry<String, Object> entry : streamInfo.entrySet()) {
                    redisTemplate.opsForHash().put(redisKey, entry.getKey(), entry.getValue());
                }
            }

            // 保存分析结果
            if (result != null) {
                // 保存主要结果
                if (result.containsKey("person_count")) {
                    redisTemplate.opsForHash().put(redisKey, "person_count", result.get("person_count"));
                }

                if (result.containsKey("helmet_count")) {
                    redisTemplate.opsForHash().put(redisKey, "helmet_count", result.get("helmet_count"));
                }

                if (result.containsKey("gap_person_and_helmet")) {
                    redisTemplate.opsForHash().put(redisKey, "gap_person_and_helmet", result.get("gap_person_and_helmet"));

                    // 检查是否有未佩戴安全帽的情况，如果有则保存到数据库
                    Object gapObj = result.get("gap_person_and_helmet");
                    int gap = 0;
                    if (gapObj instanceof Integer) {
                        gap = (Integer) gapObj;
                    } else if (gapObj instanceof Number) {
                        gap = ((Number) gapObj).intValue();
                    } else if (gapObj != null) {
                        try {
                            gap = Integer.parseInt(gapObj.toString());
                        } catch (NumberFormatException e) {
                            logger.warn("无法解析gap_person_and_helmet值: {}", gapObj);
                        }
                    }

                    if (gap > 0) {
                        saveDangerRecord(streamInfo, result, redisKey, gap);
                    }
                }

                // 保存帧结果
                if (result.containsKey("image_base64")) {
                    // 保存图像数据
                    String base64Image = (String) result.get("image_base64");
                    byte[] imageBytes = Base64.getDecoder().decode(base64Image);
                    redisTemplate.opsForHash().put(redisKey, "img_byte", imageBytes);
                }

                // 添加时间戳
                redisTemplate.opsForHash().put(redisKey, "update_time", System.currentTimeMillis());
            }
        } catch (Exception e) {
            logger.error("保存到Redis时出错", e);
        }
    }

    /**
     * 保存危险记录
     */
    private void saveDangerRecord(Map<String, Object> streamInfo, Map<String, Object> result, String redisKey, int gap) {
        try {
            // 获取区域ID
            Long areaId = null;
            if (streamInfo.containsKey("areald") || streamInfo.containsKey("areaId")) {
                Object areaIdObj = streamInfo.getOrDefault("areald", streamInfo.get("areaId"));
                if (areaIdObj instanceof Long) {
                    areaId = (Long) areaIdObj;
                } else if (areaIdObj instanceof Number) {
                    areaId = ((Number) areaIdObj).longValue();
                } else if (areaIdObj != null) {
                    try {
                        areaId = Long.parseLong(areaIdObj.toString());
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析areaId值: {}", areaIdObj);
                    }
                }
            }

            // 如果没有找到areaId，尝试从streamInfo的id中获取
            if (areaId == null && streamInfo.containsKey("id")) {
                Object idObj = streamInfo.get("id");
                if (idObj instanceof Long) {
                    areaId = (Long) idObj;
                } else if (idObj instanceof Number) {
                    areaId = ((Number) idObj).longValue();
                } else if (idObj != null) {
                    try {
                        areaId = Long.parseLong(idObj.toString());
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析id值作为areaId: {}", idObj);
                    }
                }
            }

            // 如果仍然没有找到areaId，则使用默认值1
            if (areaId == null) {
                areaId = 1L;
                logger.warn("未找到有效的areaId，使用默认值1");
            }

            // 构建记录文件路径
            String recordFile = null;
            if (result.containsKey("frame_count")) {
                // 获取redisKey的安全文件名版本
                String safeKey = redisKey.replace(":", "_").replace("/", "_");

                // 根据操作系统确定路径格式
                String osName = System.getProperty("os.name").toLowerCase();
                if (osName.contains("win")) {
                    // Windows系统
                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + result.get("frame_count") + ".jpg";
                } else {
                    // Linux系统
                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + result.get("frame_count") + ".jpg";
                }
            } else {
                // 如果没有frame_count，则使用时间戳
                String safeKey = redisKey.replace(":", "_").replace("/", "_");
                String timestamp = String.valueOf(System.currentTimeMillis());

                // 根据操作系统确定路径格式
                String osName = System.getProperty("os.name").toLowerCase();
                if (osName.contains("win")) {
                    // Windows系统
                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + timestamp + ".jpg";
                } else {
                    // Linux系统
                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + timestamp + ".jpg";
                }
            }

            // 创建并保存危险情况记录
            PersonDangerInfoSaveReqVO dangerInfo = new PersonDangerInfoSaveReqVO();
            dangerInfo.setAreaId(areaId);
            dangerInfo.setIsWearingHelmet(0); // 1表示未佩戴安全帽
            dangerInfo.setIsDangerousBehavior(0); // 1表示存在危险行为
            dangerInfo.setRecordTime(LocalDateTime.now());
            dangerInfo.setRecordFile(recordFile);

            // 保存到数据库
            Long id = personDangerInfoService.createPersonDangerInfo(dangerInfo);
            logger.info("已保存人员危险情况记录到数据库，ID={}, 区域ID={}, 未佩戴安全帽人数={}, 图片路径={}",
                       id, areaId, gap, recordFile);

            // 创建并保存报警信息
            AlarmInfoSaveReqVO alarmInfo = new AlarmInfoSaveReqVO();
            alarmInfo.setAreaId(areaId);
            alarmInfo.setAlarmType("2"); // 2表示未佩戴安全帽
            alarmInfo.setConfirmedStatus("0"); // 0表示未确认
            alarmInfo.setAlarmTime(LocalDateTime.now());
            alarmInfo.setAlarmPic(recordFile); // 使用相同的图片路径

            // 保存到数据库
            Long alarmId = alarmInfoService.createAlarmInfo(alarmInfo);
            logger.info("已保存报警信息记录到数据库，ID={}, 区域ID={}, 报警类型=未佩戴安全帽, 图片路径={}",
                       alarmId, areaId, recordFile);
        } catch (Exception e) {
            logger.error("保存人员危险情况记录或报警信息到数据库时出错", e);
        }
    }

    // 辅助方法，从Map中获取Long类型的值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
