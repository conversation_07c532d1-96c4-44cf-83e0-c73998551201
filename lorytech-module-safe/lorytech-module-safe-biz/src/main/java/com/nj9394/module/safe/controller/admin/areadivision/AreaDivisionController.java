package com.nj9394.module.safe.controller.admin.areadivision;

import cn.hutool.core.collection.CollectionUtil;
import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.framework.excel.core.util.ExcelUtils;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionPageReqVO;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionRespVO;
import com.nj9394.module.safe.controller.admin.areadivision.vo.AreaDivisionSaveReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoRespVO;
import com.nj9394.module.safe.dal.dataobject.areadivision.AreaDivisionDO;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;
import com.nj9394.module.safe.service.areadivision.AreaDivisionService;
import com.nj9394.module.safe.service.factoryinfo.FactoryInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nj9394.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 区域划分")
@RestController
@RequestMapping("/safe/area-division")
@Validated
public class AreaDivisionController {

    @Resource
    private AreaDivisionService areaDivisionService;

    @Resource
    private FactoryInfoService factoryInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建区域划分")
    @PreAuthorize("@ss.hasPermission('safe:area-division:create')")
    public CommonResult<Long> createAreaDivision(@Valid @RequestBody AreaDivisionSaveReqVO createReqVO) {
        return success(areaDivisionService.createAreaDivision(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新区域划分")
    @PreAuthorize("@ss.hasPermission('safe:area-division:update')")
    public CommonResult<Boolean> updateAreaDivision(@Valid @RequestBody AreaDivisionSaveReqVO updateReqVO) {
        areaDivisionService.updateAreaDivision(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除区域划分")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:area-division:delete')")
    public CommonResult<Boolean> deleteAreaDivision(@RequestParam("id") Long id) {
        areaDivisionService.deleteAreaDivision(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得区域划分")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:area-division:query')")
    public CommonResult<AreaDivisionRespVO> getAreaDivision(@RequestParam("id") Long id) {
        AreaDivisionDO areaDivision = areaDivisionService.getAreaDivision(id);
        AreaDivisionRespVO bean = BeanUtils.toBean(areaDivision, AreaDivisionRespVO.class);
        bean.setFactory(BeanUtils.toBean(factoryInfoService.getFactoryInfo(bean.getFactoryId()), FactoryInfoRespVO.class));
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得区域划分分页")
    @PreAuthorize("@ss.hasPermission('safe:area-division:query')")
    public CommonResult<PageResult<AreaDivisionRespVO>> getAreaDivisionPage(@Valid AreaDivisionPageReqVO pageReqVO) {
        PageResult<AreaDivisionDO> pageResult = areaDivisionService.getAreaDivisionPage(pageReqVO);
        FactoryInfoPageReqVO factoryInfoPageReqVO = new FactoryInfoPageReqVO();
        factoryInfoPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<AreaDivisionRespVO> page = BeanUtils.toBean(pageResult, AreaDivisionRespVO.class);
        if (!CollectionUtil.isEmpty(page.getList())) {
            List<FactoryInfoDO> list = factoryInfoService.getFactoryInfoPage(factoryInfoPageReqVO).getList();
            Map<Long, FactoryInfoDO> map = list.stream().collect(Collectors.toMap(FactoryInfoDO::getId, Function.identity()));
            page.getList().stream().forEach(item -> {
                FactoryInfoDO factory = map.get(item.getFactoryId());
                item.setFactory(factory == null ? null : BeanUtils.toBean(factory, FactoryInfoRespVO.class));
            });
        }
        return success(page);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出区域划分 Excel")
    @PreAuthorize("@ss.hasPermission('safe:area-division:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAreaDivisionExcel(@Valid AreaDivisionPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AreaDivisionDO> list = areaDivisionService.getAreaDivisionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "区域划分.xls", "数据", AreaDivisionRespVO.class,
                BeanUtils.toBean(list, AreaDivisionRespVO.class));
    }

    @GetMapping("/area-list")
    @Operation(summary = "获得区域划分列表")
    @PreAuthorize("@ss.hasPermission('safe:area-division:query')")
    public CommonResult<List<AreaDivisionRespVO>> getAreaDivisionList(@Valid AreaDivisionPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AreaDivisionDO> list = areaDivisionService.getAreaDivisionPage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, AreaDivisionRespVO.class));
    }

}