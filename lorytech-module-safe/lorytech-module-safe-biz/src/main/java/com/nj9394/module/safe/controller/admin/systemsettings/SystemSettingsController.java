package com.nj9394.module.safe.controller.admin.systemsettings;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.systemsettings.vo.*;
import com.nj9394.module.safe.dal.dataobject.systemsettings.SystemSettingsDO;
import com.nj9394.module.safe.service.systemsettings.SystemSettingsService;

@Tag(name = "管理后台 - SAFE 安全系统设置")
@RestController
@RequestMapping("/safe/system-settings")
@Validated
public class SystemSettingsController {

    @Resource
    private SystemSettingsService systemSettingsService;

    @PostMapping("/create")
    @Operation(summary = "创建SAFE 安全系统设置")
    @PreAuthorize("@ss.hasPermission('safe:system-settings:create')")
    public CommonResult<Long> createSystemSettings(@Valid @RequestBody SystemSettingsSaveReqVO createReqVO) {
        return success(systemSettingsService.createSystemSettings(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新SAFE 安全系统设置")
    @PreAuthorize("@ss.hasPermission('safe:system-settings:update')")
    public CommonResult<Boolean> updateSystemSettings(@Valid @RequestBody SystemSettingsSaveReqVO updateReqVO) {
        systemSettingsService.updateSystemSettings(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除SAFE 安全系统设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:system-settings:delete')")
    public CommonResult<Boolean> deleteSystemSettings(@RequestParam("id") Long id) {
        systemSettingsService.deleteSystemSettings(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得SAFE 安全系统设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:system-settings:query')")
    public CommonResult<SystemSettingsRespVO> getSystemSettings(@RequestParam("id") Long id) {
        SystemSettingsDO systemSettings = systemSettingsService.getSystemSettings(id);
        return success(BeanUtils.toBean(systemSettings, SystemSettingsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得SAFE 安全系统设置分页")
    @PreAuthorize("@ss.hasPermission('safe:system-settings:query')")
    public CommonResult<PageResult<SystemSettingsRespVO>> getSystemSettingsPage(@Valid SystemSettingsPageReqVO pageReqVO) {
        PageResult<SystemSettingsDO> pageResult = systemSettingsService.getSystemSettingsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SystemSettingsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出SAFE 安全系统设置 Excel")
    @PreAuthorize("@ss.hasPermission('safe:system-settings:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSystemSettingsExcel(@Valid SystemSettingsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SystemSettingsDO> list = systemSettingsService.getSystemSettingsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "SAFE 安全系统设置.xls", "数据", SystemSettingsRespVO.class,
                        BeanUtils.toBean(list, SystemSettingsRespVO.class));
    }

}