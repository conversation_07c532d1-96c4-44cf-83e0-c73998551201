package com.nj9394.module.safe.controller.admin.alarminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 报警统计数据 Response VO")
@Data
public class AlarmStatsRespVO {

    @Schema(description = "总报警次数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer alarmCount;

    @Schema(description = "已处理次数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer handledCount;

    @Schema(description = "已逾期次数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer overdueCount;

    @Schema(description = "环比变化（百分比）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer changePercentage;

} 