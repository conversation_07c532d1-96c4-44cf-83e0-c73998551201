package com.nj9394.module.safe.service.historicaldataquery;

import java.util.*;
import javax.validation.*;
import com.nj9394.module.safe.controller.admin.historicaldataquery.vo.*;
import com.nj9394.module.safe.dal.dataobject.historicaldataquery.HistoricalDataQueryDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;

/**
 * 安全分析数据归档 Service 接口
 *
 * <AUTHOR>
 */
public interface HistoricalDataQueryService {

    /**
     * 创建安全分析数据归档
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHistoricalDataQuery(@Valid HistoricalDataQuerySaveReqVO createReqVO);

    /**
     * 更新安全分析数据归档
     *
     * @param updateReqVO 更新信息
     */
    void updateHistoricalDataQuery(@Valid HistoricalDataQuerySaveReqVO updateReqVO);

    /**
     * 删除安全分析数据归档
     *
     * @param id 编号
     */
    void deleteHistoricalDataQuery(Long id);

    /**
     * 获得安全分析数据归档
     *
     * @param id 编号
     * @return 安全分析数据归档
     */
    HistoricalDataQueryDO getHistoricalDataQuery(Long id);

    /**
     * 获得安全分析数据归档分页
     *
     * @param pageReqVO 分页查询
     * @return 安全分析数据归档分页
     */
    PageResult<HistoricalDataQueryDO> getHistoricalDataQueryPage(HistoricalDataQueryPageReqVO pageReqVO);

}