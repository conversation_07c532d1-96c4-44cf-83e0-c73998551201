package com.nj9394.module.safe.service.python;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nj9394.module.safe.controller.admin.alarminfo.vo.AlarmInfoSaveReqVO;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.PersonDangerInfoSaveReqVO;
import com.nj9394.module.safe.service.alarminfo.AlarmInfoService;
import com.nj9394.module.safe.service.persondangerinfo.PersonDangerInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.CompletableFuture;

/**
 * Python服务类，用于调用Python脚本并处理结果
 */
@Service
public class PythonService {

    private static final Logger logger = LoggerFactory.getLogger(PythonService.class);
    private static final String PYTHON_MODEL_DIR = "/home/<USER>/work/model_het";
    private static final String PYTHON_SCRIPT = "imageToResult.py";
    private static final String REDIS_KEY_PREFIX = "het_img:";
    private static final int INTERVAL_SECONDS = 1; // 持续监控模式下的间隔时间
    private static final int MAX_RETRIES = 5; // 最大重试次数
    private static final int RETRY_DELAY_SECONDS = 5; // 重试延迟（秒）
    private static final boolean USE_GPU = true; // 是否启用GPU加速（如果可用）

    // Python解释器路径，根据操作系统设置不同的默认值
    private static final String PYTHON_INTERPRETER = getPythonInterpreter();

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PersonDangerInfoService personDangerInfoService;

    @Resource
    private AlarmInfoService alarmInfoService;

    @Value("${safe.danger-url}")
    private String dangerUrl;

    // 存储正在运行的Python进程
    private final Map<String, Process> runningProcesses = new ConcurrentHashMap<>();
    // 存储停止标志
    private final Map<String, AtomicBoolean> stopFlags = new ConcurrentHashMap<>();
    // 存储重试计数
    private final Map<String, AtomicInteger> retryCounters = new ConcurrentHashMap<>();

    /**
     * 处理视频流
     *
     * @param streamInfo 视频流信息
     * @param rtspUrl RTSP URL
     */
    public void processVideoStream(Map<String, Object> streamInfo, String rtspUrl) {
        Long id = getLongValue(streamInfo, "id");
        Long cameraId = getLongValue(streamInfo, "camera_id");

        if (id == null || cameraId == null) {
            logger.error("视频流信息不完整，无法处理");
            return;
        }

        String streamKey = id + ":" + cameraId;
        String redisKey = REDIS_KEY_PREFIX + streamKey;

        // 记录开始处理视频流
        logger.info("开始处理视频流: ID={}, CameraID={}, RTSP={}", id, cameraId, rtspUrl);

        // 检查是否已经有相同的流在处理
        if (runningProcesses.containsKey(streamKey)) {
            // 检查进程是否仍在运行
            Process existingProcess = runningProcesses.get(streamKey);
            if (existingProcess.isAlive()) {
                logger.info("视频流已在处理中，ID={}, CameraID={}", id, cameraId);
                return;
            } else {
                // 进程已经结束，从映射中移除
                logger.warn("视频流进程已结束，将重新启动, ID={}, CameraID={}", id, cameraId);
                runningProcesses.remove(streamKey);
            }
        }

        // 创建或重置停止标志
        AtomicBoolean stopFlag = new AtomicBoolean(false);
        stopFlags.put(streamKey, stopFlag);

        // 创建或重置重试计数器
        AtomicInteger retryCounter = new AtomicInteger(0);
        retryCounters.put(streamKey, retryCounter);

        // 启动线程处理视频流
        startProcessingThread(streamInfo, rtspUrl, streamKey, redisKey, stopFlag, retryCounter);

        logger.info("已启动视频流处理线程，ID={}, CameraID={}", id, cameraId);
    }

    /**
     * 启动处理线程
     */
    private void startProcessingThread(Map<String, Object> streamInfo, String rtspUrl, String streamKey, String redisKey,
                                      AtomicBoolean stopFlag, AtomicInteger retryCounter) {
        Thread processingThread = new Thread(() -> {
            try {
                // 启动Python进程
                Process processInstance = startPythonProcess(rtspUrl, redisKey);
                if (processInstance == null) {
                    logger.error("无法启动Python进程，StreamKey={}", streamKey);
                    handleProcessFailure(streamInfo, rtspUrl, streamKey, redisKey, stopFlag, retryCounter);
                    return;
                }

                // 存储进程实例
                runningProcesses.put(streamKey, processInstance);

                // 读取标准输出
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(processInstance.getInputStream()), 16384)) {
                    String line;
                    while ((line = reader.readLine()) != null && !stopFlag.get()) {
                        // 处理Python脚本的输出
                        if (line.startsWith("{")) {
                            // 尝试解析JSON输出
                            try {
                                // 使用异步处理，避免阻塞读取线程
                                final String jsonLine = line;
                                CompletableFuture.runAsync(() -> {
                                    try {
                                        Map<String, Object> result = objectMapper.readValue(jsonLine, Map.class);
                                        // 保存到Redis
                                        saveToRedis(redisKey, streamInfo, result);
                                        if (logger.isDebugEnabled()) {
                                            logger.debug("已更新Redis中的数据，键={}", redisKey);
                                        }
                                        // 重置重试计数器，因为成功处理了一帧
                                        retryCounter.set(0);
                                    } catch (Exception e) {
                                        logger.warn("解析Python输出失败: {}", jsonLine, e);
                                    }
                                });
                            } catch (Exception e) {
                                logger.warn("处理Python输出时出错: {}", line, e);
                            }
                        } else if (line.startsWith("[INFO]") && logger.isDebugEnabled()) {
                            // 只在调试模式下输出INFO日志
                            logger.debug("Python输出 [{}]: {}", streamKey, line);
                        }
                    }
                }

                // 读取错误输出
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(processInstance.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null && !stopFlag.get()) {
                        logger.warn("Python错误输出 [{}]: {}", streamKey, line);
                    }
                }

                // 等待进程结束
                int exitCode = processInstance.waitFor();
                logger.info("Python进程已结束，退出码={}, StreamKey={}", exitCode, streamKey);

                // 如果进程非正常退出且没有设置停止标志，则尝试重启
                if (exitCode != 0 && !stopFlag.get()) {
                    handleProcessFailure(streamInfo, rtspUrl, streamKey, redisKey, stopFlag, retryCounter);
                }

            } catch (Exception e) {
                logger.error("处理视频流时出错，StreamKey={}", streamKey, e);
                handleProcessFailure(streamInfo, rtspUrl, streamKey, redisKey, stopFlag, retryCounter);
            }
        });

        processingThread.setDaemon(true);
        processingThread.start();
    }

    /**
     * 处理进程失败的情况
     */
    private void handleProcessFailure(Map<String, Object> streamInfo, String rtspUrl, String streamKey, String redisKey,
                                     AtomicBoolean stopFlag, AtomicInteger retryCounter) {
        // 增加重试计数
        int retryCount = retryCounter.incrementAndGet();

        // 清理当前进程
        Process currentProcess = runningProcesses.remove(streamKey);
        if (currentProcess != null) {
            try {
                currentProcess.destroy();
            } catch (Exception e) {
                logger.warn("销毁进程时出错: {}", e.getMessage());
            }
        }

        // 检查是否达到最大重试次数
        if (retryCount > MAX_RETRIES) {
            logger.error("达到最大重试次数({}次)，停止处理视频流，StreamKey={}", MAX_RETRIES, streamKey);
            stopFlags.remove(streamKey);
            retryCounters.remove(streamKey);
            return;
        }

        // 等待一段时间后重试
        logger.info("将在{}秒后重试处理视频流，当前重试次数: {}/{}, StreamKey={}",
                   RETRY_DELAY_SECONDS, retryCount, MAX_RETRIES, streamKey);

        try {
            Thread.sleep(RETRY_DELAY_SECONDS * 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("重试等待被中断");
            return;
        }

        // 如果已设置停止标志，则不再重试
        if (stopFlag.get()) {
            logger.info("检测到停止标志，不再重试处理视频流，StreamKey={}", streamKey);
            stopFlags.remove(streamKey);
            retryCounters.remove(streamKey);
            return;
        }

        // 重新启动处理线程
        logger.info("重新启动处理视频流，StreamKey={}", streamKey);
        startProcessingThread(streamInfo, rtspUrl, streamKey, redisKey, stopFlag, retryCounter);
    }

    /**
     * 停止处理视频流
     *
     * @param id 视频流ID
     * @param cameraId 摄像头ID
     */
    public void stopProcessingStream(Long id, Long cameraId) {
        String streamKey = id + ":" + cameraId;

        // 设置停止标志
        AtomicBoolean stopFlag = stopFlags.get(streamKey);
        if (stopFlag != null) {
            stopFlag.set(true);
        }

        // 终止进程
        Process process = runningProcesses.get(streamKey);
        if (process != null) {
            process.destroy();
            logger.info("已发送停止信号给视频流处理进程，ID={}, CameraID={}", id, cameraId);
        } else {
            logger.warn("未找到视频流处理进程，ID={}, CameraID={}", id, cameraId);
        }
    }

    /**
     * 启动Python进程
     *
     * @param rtspUrl RTSP URL
     * @param redisKey Redis键，用于创建子目录
     * @return Python进程
     */
    private Process startPythonProcess(String rtspUrl, String redisKey) {
        try {
            // 获取Python脚本的完整路径
            File scriptFile = new File(PYTHON_MODEL_DIR, PYTHON_SCRIPT);
            String scriptPath = scriptFile.getAbsolutePath();

            // 构建Python命令
            List<String> commands = new ArrayList<>();
            commands.add(PYTHON_INTERPRETER); // 使用系统相关的Python解释器路径
            commands.add(scriptPath);
            commands.add("--rtsp");
            commands.add(rtspUrl);
            commands.add("--continuous");   //持续监控
            commands.add("--interval");
            commands.add(String.valueOf(INTERVAL_SECONDS));  //间隔时间
            commands.add("--minimal");  //精简模式
            commands.add("--redis-key");
            commands.add(redisKey);  //Redis键，用于创建子目录

            // 如果不使用GPU，则添加--use-cpu参数
            if (!USE_GPU) {
                commands.add("--use-cpu");
                logger.info("将使用CPU进行推理");
            } else {
                logger.info("将尝试使用GPU进行推理（如果可用）");
            }

            // 调试模式
            if (logger.isDebugEnabled()) {
                commands.add("--debug");
            }

            ProcessBuilder processBuilder = new ProcessBuilder(commands);

            // 设置工作目录
            processBuilder.directory(new File(PYTHON_MODEL_DIR));

            // 在Linux系统上，设置环境变量确保输出目录权限
            Map<String, String> env = processBuilder.environment();
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("linux")) {
                // 使用当前用户的home目录下的数据目录
                String userHome = System.getProperty("user.home");
                env.put("OUTPUT_DIR", userHome + "/data/outHet");
                logger.info("在Linux系统上设置输出目录为: {}", userHome + "/data/outHet");
            }

            // 记录完整命令
            StringBuilder commandStr = new StringBuilder();
            for (String cmd : processBuilder.command()) {
                commandStr.append(cmd).append(" ");
            }
            logger.info("执行命令: {}", commandStr.toString().trim());

            // 重定向错误流到标准输出
            processBuilder.redirectErrorStream(false);

            // 启动进程
            Process process = processBuilder.start();
            logger.info("已启动Python进程，RTSP={}, RedisKey={}", rtspUrl, redisKey);

            return process;

        } catch (Exception e) {
            logger.error("启动Python进程时出错", e);
            return null;
        }
    }

    /**
     * 保存到Redis
     *
     * @param redisKey Redis键
     * @param streamInfo 视频流信息
     * @param result 分析结果
     */
    private void saveToRedis(String redisKey, Map<String, Object> streamInfo, Map<String, Object> result) {
        try {
            // 保存视频流信息（仅在首次保存）
            if (!redisTemplate.hasKey(redisKey)) {
                for (Map.Entry<String, Object> entry : streamInfo.entrySet()) {
                    redisTemplate.opsForHash().put(redisKey, entry.getKey(), entry.getValue());
                }
            }

            // 保存分析结果
            if (result != null) {
                // 保存主要结果
                if (result.containsKey("person_count")) {
                    redisTemplate.opsForHash().put(redisKey, "person_count", result.get("person_count"));
                }

                if (result.containsKey("helmet_count")) {
                    redisTemplate.opsForHash().put(redisKey, "helmet_count", result.get("helmet_count"));
                }

                if (result.containsKey("gap_person_and_helmet")) {
                    redisTemplate.opsForHash().put(redisKey, "gap_person_and_helmet", result.get("gap_person_and_helmet"));

                    // 检查是否有未佩戴安全帽的情况，如果有则保存到数据库
                    Object gapObj = result.get("gap_person_and_helmet");
                    int gap = 0;
                    if (gapObj instanceof Integer) {
                        gap = (Integer) gapObj;
                    } else if (gapObj instanceof Number) {
                        gap = ((Number) gapObj).intValue();
                    } else if (gapObj != null) {
                        try {
                            gap = Integer.parseInt(gapObj.toString());
                        } catch (NumberFormatException e) {
                            logger.warn("无法解析gap_person_and_helmet值: {}", gapObj);
                        }
                    }

                    if (gap > 0) {
                        try {
                            // 获取区域ID
                            Long areaId = null;
                            if (streamInfo.containsKey("areald") || streamInfo.containsKey("areaId")) {
                                Object areaIdObj = streamInfo.getOrDefault("areald", streamInfo.get("areaId"));
                                if (areaIdObj instanceof Long) {
                                    areaId = (Long) areaIdObj;
                                } else if (areaIdObj instanceof Number) {
                                    areaId = ((Number) areaIdObj).longValue();
                                } else if (areaIdObj != null) {
                                    try {
                                        areaId = Long.parseLong(areaIdObj.toString());
                                    } catch (NumberFormatException e) {
                                        logger.warn("无法解析areaId值: {}", areaIdObj);
                                    }
                                }
                            }

                            // 如果没有找到areaId，尝试从streamInfo的id中获取
                            if (areaId == null && streamInfo.containsKey("id")) {
                                Object idObj = streamInfo.get("id");
                                if (idObj instanceof Long) {
                                    areaId = (Long) idObj;
                                } else if (idObj instanceof Number) {
                                    areaId = ((Number) idObj).longValue();
                                } else if (idObj != null) {
                                    try {
                                        areaId = Long.parseLong(idObj.toString());
                                    } catch (NumberFormatException e) {
                                        logger.warn("无法解析id值作为areaId: {}", idObj);
                                    }
                                }
                            }

                            // 如果仍然没有找到areaId，则使用默认值1
                            if (areaId == null) {
                                areaId = 1L;
                                logger.warn("未找到有效的areaId，使用默认值1");
                            }

                            // 构建记录文件路径
                            String recordFile = null;
                            if (result.containsKey("frame_count")) {
                                // 获取redisKey的安全文件名版本
                                String safeKey = redisKey.replace(":", "_").replace("/", "_");

                                // 根据操作系统确定路径格式
                                String osName = System.getProperty("os.name").toLowerCase();
                                if (osName.contains("win")) {
                                    // Windows系统
                                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + result.get("frame_count") + ".jpg";
                                } else {
                                    // Linux系统
                                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + result.get("frame_count") + ".jpg";
                                }
                            } else {
                                // 如果没有frame_count，则使用时间戳
                                String safeKey = redisKey.replace(":", "_").replace("/", "_");
                                String timestamp = String.valueOf(System.currentTimeMillis());

                                // 根据操作系统确定路径格式
                                String osName = System.getProperty("os.name").toLowerCase();
                                if (osName.contains("win")) {
                                    // Windows系统
                                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + timestamp + ".jpg";
                                } else {
                                    // Linux系统
                                    recordFile = dangerUrl + "data/outHet/" + safeKey + "/result_" + timestamp + ".jpg";
                                }
                            }

                            // 创建并保存危险情况记录
                            PersonDangerInfoSaveReqVO dangerInfo = new PersonDangerInfoSaveReqVO();
                            dangerInfo.setAreaId(areaId);
                            dangerInfo.setIsWearingHelmet(0); // 1表示未佩戴安全帽
                            dangerInfo.setIsDangerousBehavior(0); // 1表示存在危险行为
                            dangerInfo.setRecordTime(LocalDateTime.now());
                            dangerInfo.setRecordFile(recordFile);

                            // 保存到数据库
                            Long id = personDangerInfoService.createPersonDangerInfo(dangerInfo);
                            logger.info("已保存人员危险情况记录到数据库，ID={}, 区域ID={}, 未佩戴安全帽人数={}, 图片路径={}",
                                       id, areaId, gap, recordFile);

                            // 创建并保存报警信息
                            AlarmInfoSaveReqVO alarmInfo = new AlarmInfoSaveReqVO();
                            alarmInfo.setAreaId(areaId);
                            alarmInfo.setAlarmType("2"); // 2表示未佩戴安全帽
                            alarmInfo.setConfirmedStatus("0"); // 0表示未确认
                            alarmInfo.setAlarmTime(LocalDateTime.now());
                            alarmInfo.setAlarmPic(recordFile); // 使用相同的图片路径

                            // 保存到数据库
                            Long alarmId = alarmInfoService.createAlarmInfo(alarmInfo);
                            logger.info("已保存报警信息记录到数据库，ID={}, 区域ID={}, 报警类型=未佩戴安全帽, 图片路径={}",
                                       alarmId, areaId, recordFile);
                        } catch (Exception e) {
                            logger.error("保存人员危险情况记录或报警信息到数据库时出错", e);
                        }
                    }
                }

                // 保存帧结果
                if (result.containsKey("image_base64")) {
                    // 保存图像数据
                    String base64Image = (String) result.get("image_base64");
                    byte[] imageBytes = java.util.Base64.getDecoder().decode(base64Image);
                    redisTemplate.opsForHash().put(redisKey, "img_byte", imageBytes);
                }

                // 添加时间戳
                redisTemplate.opsForHash().put(redisKey, "update_time", System.currentTimeMillis());
            }
        } catch (Exception e) {
            logger.error("保存到Redis时出错", e);
        }
    }

    /**
     * 关闭所有进程
     */
    public void shutdownAllProcesses() {
        for (Map.Entry<String, Process> entry : runningProcesses.entrySet()) {
            String streamKey = entry.getKey();
            Process process = entry.getValue();

            // 设置停止标志
            AtomicBoolean stopFlag = stopFlags.get(streamKey);
            if (stopFlag != null) {
                stopFlag.set(true);
            }

            // 终止进程
            process.destroy();
            logger.info("已停止视频流处理进程，StreamKey={}", streamKey);
        }

        // 清空集合
        runningProcesses.clear();
        stopFlags.clear();
        retryCounters.clear();

        logger.info("已停止所有视频流处理进程");
    }

    /**
     * 获取当前处理的视频流数量
     */
    public int getProcessingStreamCount() {
        return runningProcesses.size();
    }

    /**
     * 获取所有处理中的视频流键
     */
    public String[] getProcessingStreamKeys() {
        return runningProcesses.keySet().toArray(new String[0]);
    }

    /**
     * 获取适合当前操作系统的Python解释器路径
     * @return Python解释器路径
     */
    private static String getPythonInterpreter() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("linux")) {
            // Linux系统使用conda环境中的Python
            return "/home/<USER>/work/float_flask/.conda/bin/python";
        } else if (osName.contains("win")) {
            // Windows系统使用普通python命令
            return "python";
        } else {
            // 其他操作系统默认使用python命令
            return "/home/<USER>/work/float_flask/.conda/bin/python";
        }
    }

    // 辅助方法，从Map中获取Long类型的值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
