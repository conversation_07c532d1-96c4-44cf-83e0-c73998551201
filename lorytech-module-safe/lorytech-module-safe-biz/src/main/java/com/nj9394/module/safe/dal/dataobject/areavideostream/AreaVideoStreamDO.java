package com.nj9394.module.safe.dal.dataobject.areavideostream;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.nj9394.framework.mybatis.core.dataobject.BaseDO;

/**
 * 视频区域划分 DO
 *
 * <AUTHOR>
 */
@TableName("safe_area_video_stream")
@KeySequence("safe_area_video_stream_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaVideoStreamDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 区域ID
     *
     * 枚举 {@link TODO system_user_sex 对应的类}
     */
    private Long areaId;
    /**
     * 视频流ID
     *
     * 枚举 {@link TODO system_user_sex 对应的类}
     */
    private Long videoId;
    /**
     * 视频区域
     */
    private String videoArea;

}