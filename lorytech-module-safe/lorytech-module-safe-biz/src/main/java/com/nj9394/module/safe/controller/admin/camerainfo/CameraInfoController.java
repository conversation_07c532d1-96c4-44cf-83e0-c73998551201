package com.nj9394.module.safe.controller.admin.camerainfo;

import cn.hutool.core.collection.CollectionUtil;
import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import com.nj9394.framework.excel.core.util.ExcelUtils;
import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoRespVO;
import com.nj9394.module.safe.controller.admin.camerainfo.vo.CameraInfoSaveReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoPageReqVO;
import com.nj9394.module.safe.controller.admin.factoryinfo.vo.FactoryInfoRespVO;
import com.nj9394.module.safe.dal.dataobject.camerainfo.CameraInfoDO;
import com.nj9394.module.safe.dal.dataobject.factoryinfo.FactoryInfoDO;
import com.nj9394.module.safe.service.camerainfo.CameraInfoService;
import com.nj9394.module.safe.service.factoryinfo.FactoryInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nj9394.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 摄像头信息")
@RestController
@RequestMapping("/safe/camera-info")
@Validated
public class CameraInfoController {

    @Resource
    private CameraInfoService cameraInfoService;

    @Resource
    private FactoryInfoService factoryInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建摄像头信息")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:create')")
    public CommonResult<Long> createCameraInfo(@Valid @RequestBody CameraInfoSaveReqVO createReqVO) {
        return success(cameraInfoService.createCameraInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新摄像头信息")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:update')")
    public CommonResult<Boolean> updateCameraInfo(@Valid @RequestBody CameraInfoSaveReqVO updateReqVO) {
        cameraInfoService.updateCameraInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除摄像头信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:camera-info:delete')")
    public CommonResult<Boolean> deleteCameraInfo(@RequestParam("id") Long id) {
        cameraInfoService.deleteCameraInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得摄像头信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:query')")
    public CommonResult<CameraInfoRespVO> getCameraInfo(@RequestParam("id") Long id) {
        CameraInfoDO cameraInfo = cameraInfoService.getCameraInfo(id);
        CameraInfoRespVO bean = BeanUtils.toBean(cameraInfo, CameraInfoRespVO.class);
        bean.setFactory(BeanUtils.toBean(factoryInfoService.getFactoryInfo(bean.getFactoryId()), FactoryInfoRespVO.class));
        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得摄像头信息分页")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:query')")
    public CommonResult<PageResult<CameraInfoRespVO>> getCameraInfoPage(@Valid CameraInfoPageReqVO pageReqVO) {
        PageResult<CameraInfoDO> pageResult = cameraInfoService.getCameraInfoPage(pageReqVO);
        FactoryInfoPageReqVO factoryInfoPageReqVO = new FactoryInfoPageReqVO();
        factoryInfoPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<CameraInfoRespVO> page = BeanUtils.toBean(pageResult, CameraInfoRespVO.class);
        if (!CollectionUtil.isEmpty(page.getList())) {
            List<FactoryInfoDO> list = factoryInfoService.getFactoryInfoPage(factoryInfoPageReqVO).getList();
            Map<Long, FactoryInfoDO> map = list.stream().collect(Collectors.toMap(FactoryInfoDO::getId, Function.identity()));
            page.getList().stream().forEach(item -> {
                FactoryInfoDO factory = map.get(item.getFactoryId());
                item.setFactory(factory == null ? null : BeanUtils.toBean(factory, FactoryInfoRespVO.class));
            });
        }
        return success(page);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出摄像头信息 Excel")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCameraInfoExcel(@Valid CameraInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CameraInfoDO> list = cameraInfoService.getCameraInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "摄像头信息.xls", "数据", CameraInfoRespVO.class,
                        BeanUtils.toBean(list, CameraInfoRespVO.class));
    }

    @GetMapping("/camera-list")
    @Operation(summary = "获得摄像头信息列表")
    @PreAuthorize("@ss.hasPermission('safe:camera-info:query')")
    public CommonResult<List<CameraInfoRespVO>> getCameraInfoList(@Valid CameraInfoPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CameraInfoDO> list = cameraInfoService.getCameraInfoPage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, CameraInfoRespVO.class));
    }

}