package com.nj9394.module.safe.service.persondangerinfo;

import com.nj9394.framework.tenant.core.context.TenantContextHolder;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.nj9394.module.safe.controller.admin.persondangerinfo.vo.*;
import com.nj9394.module.safe.dal.dataobject.persondangerinfo.PersonDangerInfoDO;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.util.object.BeanUtils;

import com.nj9394.module.safe.dal.mysql.persondangerinfo.PersonDangerInfoMapper;

import static com.nj9394.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nj9394.module.safe.enums.ErrorCodeConstants.*;

/**
 * 人员危险情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PersonDangerInfoServiceImpl implements PersonDangerInfoService {

    @Resource
    private PersonDangerInfoMapper personDangerInfoMapper;

    @Override
    public Long createPersonDangerInfo(PersonDangerInfoSaveReqVO createReqVO) {
        // 插入
        PersonDangerInfoDO personDangerInfo = BeanUtils.toBean(createReqVO, PersonDangerInfoDO.class);
        TenantContextHolder.setTenantId(1L);
        personDangerInfoMapper.insert(personDangerInfo);
        // 返回
        return personDangerInfo.getId();
    }

    @Override
    public void updatePersonDangerInfo(PersonDangerInfoSaveReqVO updateReqVO) {
        // 校验存在
        validatePersonDangerInfoExists(updateReqVO.getId());
        // 更新
        PersonDangerInfoDO updateObj = BeanUtils.toBean(updateReqVO, PersonDangerInfoDO.class);
        personDangerInfoMapper.updateById(updateObj);
    }

    @Override
    public void deletePersonDangerInfo(Long id) {
        // 校验存在
        validatePersonDangerInfoExists(id);
        // 删除
        personDangerInfoMapper.deleteById(id);
    }

    private void validatePersonDangerInfoExists(Long id) {
        if (personDangerInfoMapper.selectById(id) == null) {
            throw exception(PERSON_DANGER_INFO_NOT_EXISTS);
        }
    }

    @Override
    public PersonDangerInfoDO getPersonDangerInfo(Long id) {
        return personDangerInfoMapper.selectById(id);
    }

    @Override
    public PageResult<PersonDangerInfoDO> getPersonDangerInfoPage(PersonDangerInfoPageReqVO pageReqVO) {
        return personDangerInfoMapper.selectPage(pageReqVO);
    }

}
