package com.nj9394.module.safe.dal.mysql.systemsettings;

import java.util.*;

import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nj9394.framework.mybatis.core.mapper.BaseMapperX;
import com.nj9394.module.safe.dal.dataobject.systemsettings.SystemSettingsDO;
import org.apache.ibatis.annotations.Mapper;
import com.nj9394.module.safe.controller.admin.systemsettings.vo.*;

/**
 * SAFE 安全系统设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SystemSettingsMapper extends BaseMapperX<SystemSettingsDO> {

    default PageResult<SystemSettingsDO> selectPage(SystemSettingsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SystemSettingsDO>()
                .likeIfPresent(SystemSettingsDO::getSettingName, reqVO.getSettingName())
                .eqIfPresent(SystemSettingsDO::getSettingValue, reqVO.getSettingValue())
                .betweenIfPresent(SystemSettingsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SystemSettingsDO::getId));
    }

}