package com.nj9394.module.safe.controller.admin.modelexecutionrecord;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.nj9394.framework.common.pojo.PageParam;
import com.nj9394.framework.common.pojo.PageResult;
import com.nj9394.framework.common.pojo.CommonResult;
import com.nj9394.framework.common.util.object.BeanUtils;
import static com.nj9394.framework.common.pojo.CommonResult.success;

import com.nj9394.framework.excel.core.util.ExcelUtils;

import com.nj9394.framework.apilog.core.annotation.ApiAccessLog;
import static com.nj9394.framework.apilog.core.enums.OperateTypeEnum.*;

import com.nj9394.module.safe.controller.admin.modelexecutionrecord.vo.*;
import com.nj9394.module.safe.dal.dataobject.modelexecutionrecord.ModelExecutionRecordDO;
import com.nj9394.module.safe.service.modelexecutionrecord.ModelExecutionRecordService;

@Tag(name = "管理后台 - 模型执行")
@RestController
@RequestMapping("/safe/model-execution-record")
@Validated
public class ModelExecutionRecordController {

    @Resource
    private ModelExecutionRecordService modelExecutionRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建模型执行")
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:create')")
    public CommonResult<Long> createModelExecutionRecord(@Valid @RequestBody ModelExecutionRecordSaveReqVO createReqVO) {
        return success(modelExecutionRecordService.createModelExecutionRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新模型执行")
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:update')")
    public CommonResult<Boolean> updateModelExecutionRecord(@Valid @RequestBody ModelExecutionRecordSaveReqVO updateReqVO) {
        modelExecutionRecordService.updateModelExecutionRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除模型执行")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:delete')")
    public CommonResult<Boolean> deleteModelExecutionRecord(@RequestParam("id") Long id) {
        modelExecutionRecordService.deleteModelExecutionRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得模型执行")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:query')")
    public CommonResult<ModelExecutionRecordRespVO> getModelExecutionRecord(@RequestParam("id") Long id) {
        ModelExecutionRecordDO modelExecutionRecord = modelExecutionRecordService.getModelExecutionRecord(id);
        return success(BeanUtils.toBean(modelExecutionRecord, ModelExecutionRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得模型执行分页")
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:query')")
    public CommonResult<PageResult<ModelExecutionRecordRespVO>> getModelExecutionRecordPage(@Valid ModelExecutionRecordPageReqVO pageReqVO) {
        PageResult<ModelExecutionRecordDO> pageResult = modelExecutionRecordService.getModelExecutionRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelExecutionRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出模型执行 Excel")
    @PreAuthorize("@ss.hasPermission('safe:model-execution-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportModelExecutionRecordExcel(@Valid ModelExecutionRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ModelExecutionRecordDO> list = modelExecutionRecordService.getModelExecutionRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "模型执行.xls", "数据", ModelExecutionRecordRespVO.class,
                        BeanUtils.toBean(list, ModelExecutionRecordRespVO.class));
    }

}