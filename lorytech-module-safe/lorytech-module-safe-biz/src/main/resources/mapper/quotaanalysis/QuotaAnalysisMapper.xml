<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nj9394.module.safe.dal.mysql.quotaanalysis.QuotaAnalysisMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.nj9394.com/MyBatis/x-plugins/
     -->

    <!-- 查询厂区人流量数据 -->
    <select id="selectFactoryFlowRate" resultType="map">
        WITH latest_records AS (
            SELECT
                q.area_id,
                q.current_person_count,
                a.factory_id,
                a.max_allowance,
                a.max_limit,
                a.max_serious,
                q.tenant_id,
                ROW_NUMBER() OVER (PARTITION BY q.area_id ORDER BY q.record_time DESC) as rn
            FROM
                safe_quota_analysis q
            JOIN
                safe_area_division a ON q.area_id = a.id
            WHERE
                DATE(q.record_time) = CURRENT_DATE()
                <if test="factoryId != null">
                    AND a.factory_id = #{factoryId}
                </if>
                AND q.deleted = 0
        )
        SELECT
            SUM(lr.current_person_count) AS current_count,
            SUM(lr.max_allowance) AS max_allowance,
            SUM(lr.max_limit) AS max_limit,
            SUM(lr.max_serious) AS max_serious
        FROM
            latest_records lr
        WHERE
            lr.rn = 1
    </select>

</mapper>
