<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nj9394.module.safe.dal.mysql.videostreaminfo.VideoStreamInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.nj9394.com/MyBatis/x-plugins/
     -->

    <select id="selectWorkingVideoStreamWithAreaList" resultType="map">
        SELECT 
            v.*,
            avs.area_id AS areaId
        FROM 
            safe_video_stream_info v
        LEFT JOIN 
            safe_area_video_stream avs ON v.id = avs.video_id
        WHERE 
            v.is_working = 1
            AND v.deleted = 0
        ORDER BY 
            v.id ASC
    </select>

</mapper>