<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nj9394.module.safe.dal.mysql.alarminfo.AlarmInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.nj9394.com/MyBatis/x-plugins/
     -->

    <resultMap id="AlarmInfoResultMap" type="com.nj9394.module.safe.dal.dataobject.alarminfo.AlarmInfoDO">
        <id property="id" column="id"/>
        <result property="areaId" column="area_id"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="confirmedStatus" column="confirmed_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <!-- 查询指定时间范围内的报警数据 -->
    <select id="selectMonthlyAlarms" resultMap="AlarmInfoResultMap">
        SELECT ai.*
        FROM safe_alarm_info ai
        LEFT JOIN safe_area_division ad ON ai.area_id = ad.id
        <where>
            ai.alarm_time BETWEEN #{params.startDate} AND #{params.endDate}
            <if test="params.factoryId != null">
                AND ad.factory_id = #{params.factoryId}
            </if>
            AND ai.deleted = 0
        </where>
    </select>
    
    <!-- 查询指定时间范围内的每日报警数量 -->
    <select id="selectDailyAlarmCounts" resultType="map">
        SELECT 
            DATE_FORMAT(ai.alarm_time, '%Y-%m-%d') AS date,
            COUNT(ai.id) AS count
        FROM 
            safe_alarm_info ai
        LEFT JOIN 
            safe_area_division ad ON ai.area_id = ad.id
        <where>
            ai.alarm_time >= #{startTime}
            <if test="factoryId != null">
                AND ad.factory_id = #{factoryId}
            </if>
            AND ai.deleted = 0
        </where>
        GROUP BY 
            DATE_FORMAT(ai.alarm_time, '%Y-%m-%d')
        ORDER BY 
            date ASC
    </select>
    
    <!-- 查询厂区报警环比数据 -->
    <select id="selectFactoryAlarmComparison" resultType="map">
        SELECT 
            f.id AS factoryId,
            f.name AS factoryName,
            IFNULL(current_month.count, 0) AS currentMonthCount,
            IFNULL(last_month.count, 0) AS lastMonthCount
        FROM 
            safe_factory_info f
        LEFT JOIN (
            SELECT 
                ad.factory_id,
                COUNT(ai.id) AS count
            FROM 
                safe_alarm_info ai
            JOIN 
                safe_area_division ad ON ai.area_id = ad.id
            WHERE 
                ai.alarm_time BETWEEN #{currentMonthStart} AND #{currentMonthEnd}
                AND ai.deleted = 0
            GROUP BY 
                ad.factory_id
        ) current_month ON f.id = current_month.factory_id
        LEFT JOIN (
            SELECT 
                ad.factory_id,
                COUNT(ai.id) AS count
            FROM 
                safe_alarm_info ai
            JOIN 
                safe_area_division ad ON ai.area_id = ad.id
            WHERE 
                ai.alarm_time BETWEEN #{lastMonthStart} AND #{lastMonthEnd}
                AND ai.deleted = 0
            GROUP BY 
                ad.factory_id
        ) last_month ON f.id = last_month.factory_id
        WHERE 
            f.deleted = 0
        <if test="factoryId != null">
            AND f.id = #{factoryId}
        </if>
        ORDER BY 
            f.id ASC
    </select>

    <select id="selectListByIds" resultMap="AlarmInfoResultMap">
        SELECT * FROM safe_alarm_info
        WHERE deleted = 0 AND id in (#{ids})
    </select>

</mapper>
