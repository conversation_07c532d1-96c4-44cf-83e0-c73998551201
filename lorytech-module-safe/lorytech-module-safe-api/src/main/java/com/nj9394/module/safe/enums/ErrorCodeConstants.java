package com.nj9394.module.safe.enums;

import com.nj9394.framework.common.exception.ErrorCode;

/**
 * Report 错误码枚举类
 *
 * report 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== 安全设置 TODO 补充编号 ==========
    ErrorCode SYSTEM_SETTINGS_NOT_EXISTS = new ErrorCode(40000001, "安全设置不存在");
    // ========== 模型执行 TODO 补充编号 ==========
    ErrorCode MODEL_EXECUTION_RECORD_NOT_EXISTS = new ErrorCode(40100001, "模型执行不存在");
    // ========== 模型版本 TODO 补充编号 ==========
    ErrorCode MODEL_VERSION_NOT_EXISTS = new ErrorCode(40200001, "模型版本不存在");
    // ========== 人员危险情况 TODO 补充编号 ==========
    ErrorCode PERSON_DANGER_INFO_NOT_EXISTS = new ErrorCode(40300001, "人员危险情况不存在");
    // ========== 统计报表 TODO 补充编号 ==========
    ErrorCode STATISTICAL_REPORT_NOT_EXISTS = new ErrorCode(40400001, "统计报表不存在");
    // ========== 数据存储 TODO 补充编号 ==========
    ErrorCode DATA_STORAGE_NOT_EXISTS = new ErrorCode(40500001, "数据存储不存在");
    // ========== 设备信息 TODO 补充编号 ==========
    ErrorCode DEVICE_INFO_NOT_EXISTS = new ErrorCode(40600001, "设备信息不存在");
    // ========== 应急预案 TODO 补充编号 ==========
    ErrorCode EMERGENCY_PLAN_NOT_EXISTS = new ErrorCode(40700001, "应急预案不存在");
    // ========== 危险环境分析 TODO 补充编号 ==========
    ErrorCode ENVIRONMENT_DANGER_INFO_NOT_EXISTS = new ErrorCode(40800001, "危险环境分析不存在");
    // ========== 历史数据 TODO 补充编号 ==========
    ErrorCode HISTORICAL_DATA_QUERY_NOT_EXISTS = new ErrorCode(40900001, "安全分析数据归档不存在");
    // ========== 报警信息 TODO 补充编号 ==========
    ErrorCode ALARM_INFO_NOT_EXISTS = new ErrorCode(41000001, "报警信息不存在");
    // ========== 区域划分 TODO 补充编号 ==========
    ErrorCode AREA_DIVISION_NOT_EXISTS = new ErrorCode(41100001, "区域划分不存在");
    // ========== 摄像头信息 TODO 补充编号 ==========
    ErrorCode CAMERA_INFO_NOT_EXISTS = new ErrorCode(41200001, "摄像头信息不存在");
    // ========== 厂房信息 TODO 补充编号 ==========
    ErrorCode FACTORY_INFO_NOT_EXISTS = new ErrorCode(41300001, "厂房信息不存在");
    // ========== 定员分析 TODO 补充编号 ==========
    ErrorCode QUOTA_ANALYSIS_NOT_EXISTS = new ErrorCode(41400001, "定员分析不存在");
    // ========== 安全用户 TODO 补充编号 ==========
    ErrorCode USER_INFO_NOT_EXISTS = new ErrorCode(41500001, "安全用户不存在");
    // ========== 视频流信息 TODO 补充编号 ==========
    ErrorCode VIDEO_STREAM_INFO_NOT_EXISTS = new ErrorCode(41600001, "视频流信息不存在");
    // ========== 视频区域划分 TODO 补充编号 ==========
    ErrorCode AREA_VIDEO_STREAM_NOT_EXISTS = new ErrorCode(41700001, "视频区域划分不存在");
    // ========== 报警配置 TODO 补充编号 ==========
    ErrorCode ALARM_SETTING_NOT_EXISTS = new ErrorCode(41800001, "报警配置不存在");
    ErrorCode ALARM_SETTING_EXISTS = new ErrorCode(41800002, "报警类型配置已存在");
}

