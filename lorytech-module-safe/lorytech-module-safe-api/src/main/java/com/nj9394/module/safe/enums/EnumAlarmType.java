package com.nj9394.module.safe.enums;

/**
 * 报警类型
 */
public enum EnumAlarmType {

    OVER("1", "超员报警"),
    HAT("2", "未戴安全帽报警"),
    DANGER("3", "潜在危险报警");

    private String type;

    private String desc;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    EnumAlarmType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
